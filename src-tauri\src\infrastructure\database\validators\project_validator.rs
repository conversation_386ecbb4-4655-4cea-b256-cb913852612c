// 项目数据验证器
// 实现项目相关的数据验证和业务规则检查

use crate::domain::entities::project::{CreateProjectData, UpdateProjectData};
// use crate::domain::value_objects::{Priority, ProjectStatus}; // 暂时注释掉，因为 value_objects 模块不存在
use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
use crate::shared::errors::{AppError, AppResult};
use chrono::NaiveDate;

/// 项目创建数据验证器
pub struct CreateProjectValidator;

impl Validator<CreateProjectData> for CreateProjectValidator {
    fn validate(&self, data: &CreateProjectData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证项目名称
        ctx.validate(
            || ValidationUtils::validate_required(&data.name, "项目名称"),
            "name_required"
        );
        
        ctx.validate(
            || ValidationUtils::validate_length(&data.name, "项目名称", 1, 200),
            "name_length"
        );

        // 验证描述
        if let Some(description) = &data.description {
            ctx.validate(
                || ValidationUtils::validate_length(description, "项目描述", 0, 2000),
                "description_length"
            );
        }

        // 验证日期范围
        ctx.validate(
            || ValidationUtils::validate_date_range(
                data.start_date,
                data.deadline,
                "项目"
            ),
            "date_range"
        );

        // 验证预估工时
        if let Some(estimated_hours) = data.estimated_hours {
            ctx.validate(
                || ValidationUtils::validate_range(
                    estimated_hours,
                    "预估工时",
                    0,
                    10000
                ),
                "estimated_hours_range"
            );
        }

        // 验证创建者
        ctx.validate(
            || ValidationUtils::validate_required(&data.created_by, "创建者"),
            "created_by_required"
        );

        ctx.finish()
    }

    fn sanitize(&self, mut data: CreateProjectData) -> CreateProjectData {
        // 清理项目名称
        data.name = ValidationUtils::sanitize_string(data.name);
        
        // 清理描述
        data.description = ValidationUtils::sanitize_optional_string(data.description);

        // 清理创建者
        data.created_by = ValidationUtils::sanitize_string(data.created_by);

        data
    }
}

/// 项目更新数据验证器
pub struct UpdateProjectValidator;

impl Validator<UpdateProjectData> for UpdateProjectValidator {
    fn validate(&self, data: &UpdateProjectData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证项目名称
        if let Some(name) = &data.name {
            ctx.validate(
                || ValidationUtils::validate_required(name, "项目名称"),
                "name_required"
            );
            
            ctx.validate(
                || ValidationUtils::validate_length(name, "项目名称", 1, 200),
                "name_length"
            );
        }

        // 验证描述
        if let Some(Some(description)) = &data.description {
            ctx.validate(
                || ValidationUtils::validate_length(description, "项目描述", 0, 2000),
                "description_length"
            );
        }

        // 验证进度
        if let Some(progress) = data.progress {
            ctx.validate(
                || ValidationUtils::validate_range(
                    progress,
                    "项目进度",
                    0,
                    100
                ),
                "progress_range"
            );
        }

        // 验证日期范围
        if data.start_date.is_some() || data.deadline.is_some() {
            ctx.validate(
                || ValidationUtils::validate_date_range(
                    data.start_date.flatten(),
                    data.deadline.flatten(),
                    "项目"
                ),
                "date_range"
            );
        }

        // 验证预估工时
        if let Some(Some(estimated_hours)) = data.estimated_hours {
            ctx.validate(
                || ValidationUtils::validate_range(
                    estimated_hours,
                    "预估工时",
                    0,
                    10000
                ),
                "estimated_hours_range"
            );
        }

        // 验证实际工时
        if let Some(actual_hours) = data.actual_hours {
            ctx.validate(
                || ValidationUtils::validate_range(
                    actual_hours,
                    "实际工时",
                    0,
                    100000
                ),
                "actual_hours_range"
            );
        }

        ctx.finish()
    }

    fn sanitize(&self, mut data: UpdateProjectData) -> UpdateProjectData {
        // 清理项目名称
        if let Some(name) = data.name {
            data.name = Some(ValidationUtils::sanitize_string(name));
        }
        
        // 清理描述
        if let Some(description) = data.description {
            data.description = Some(ValidationUtils::sanitize_optional_string(description));
        }

        data
    }
}

/// 项目业务规则验证器
pub struct ProjectBusinessRuleValidator;

impl ProjectBusinessRuleValidator {
    /// 验证项目名称在领域内的唯一性
    pub async fn validate_name_uniqueness_in_area<F, Fut>(
        name: &str,
        area_id: Option<&str>,
        exclude_id: Option<&str>,
        check_fn: F,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        let exists = check_fn().await?;
        if exists {
            let area_desc = area_id.map_or("未分类".to_string(), |id| format!("领域 {}", id));
            return Err(AppError::validation(&format!(
                "项目名称 '{}' 在 {} 中已存在",
                name, area_desc
            )));
        }
        Ok(())
    }

    /// 验证项目状态转换
    pub fn validate_status_transition(
        current_status: &ProjectStatus,
        new_status: &ProjectStatus,
    ) -> AppResult<()> {
        use ProjectStatus::*;

        let valid_transitions = match current_status {
            NotStarted => vec![InProgress, Paused, Cancelled],
            InProgress => vec![AtRisk, Paused, Completed, Cancelled],
            AtRisk => vec![InProgress, Paused, Completed, Cancelled],
            Paused => vec![InProgress, AtRisk, Cancelled],
            Completed => vec![Archived],
            Cancelled => vec![NotStarted, Archived],
            Archived => vec![], // 归档后不能再转换
        };

        if current_status == new_status {
            return Ok(()); // 相同状态总是允许的
        }

        if !valid_transitions.contains(new_status) {
            return Err(AppError::validation(&format!(
                "不能从状态 '{:?}' 转换到 '{:?}'",
                current_status, new_status
            )));
        }

        Ok(())
    }

    /// 验证项目完成条件
    pub fn validate_completion_requirements(
        progress: i32,
        status: &ProjectStatus,
        has_pending_tasks: bool,
    ) -> AppResult<()> {
        if *status == ProjectStatus::Completed {
            // 完成的项目进度必须是100%
            if progress < 100 {
                return Err(AppError::validation(
                    "项目标记为完成时，进度必须达到100%"
                ));
            }

            // 不能有未完成的任务
            if has_pending_tasks {
                return Err(AppError::validation(
                    "项目还有未完成的任务，无法标记为完成"
                ));
            }
        }

        Ok(())
    }

    /// 验证项目归档条件
    pub fn validate_archive_requirements(
        status: &ProjectStatus,
        has_active_tasks: bool,
    ) -> AppResult<()> {
        // 只有完成或取消的项目可以归档
        if *status != ProjectStatus::Completed && *status != ProjectStatus::Cancelled {
            return Err(AppError::validation(
                "只有已完成或已取消的项目可以归档"
            ));
        }

        // 不能有活跃的任务
        if has_active_tasks {
            return Err(AppError::validation(
                "项目还有活跃的任务，无法归档"
            ));
        }

        Ok(())
    }

    /// 验证项目删除权限
    pub fn validate_deletion_permission(
        project_creator: &str,
        current_user: &str,
        is_admin: bool,
        status: &ProjectStatus,
    ) -> AppResult<()> {
        // 只有项目创建者或管理员可以删除项目
        if project_creator != current_user && !is_admin {
            return Err(AppError::validation("只有项目创建者或管理员可以删除项目"));
        }

        // 已归档的项目不能删除
        if *status == ProjectStatus::Archived {
            return Err(AppError::validation("已归档的项目不能删除"));
        }

        Ok(())
    }

    /// 验证项目截止日期
    pub fn validate_deadline_constraints(
        deadline: Option<NaiveDate>,
        start_date: Option<NaiveDate>,
        status: &ProjectStatus,
    ) -> AppResult<()> {
        if let Some(deadline) = deadline {
            let today = chrono::Utc::now().date_naive();

            // 如果项目还未开始，截止日期不能是过去
            if *status == ProjectStatus::NotStarted && deadline < today {
                return Err(AppError::validation(
                    "未开始的项目截止日期不能是过去时间"
                ));
            }

            // 截止日期不能早于开始日期
            if let Some(start_date) = start_date {
                if deadline < start_date {
                    return Err(AppError::validation(
                        "截止日期不能早于开始日期"
                    ));
                }
            }

            // 截止日期不能超过3年
            let max_deadline = today + chrono::Duration::days(365 * 3);
            if deadline > max_deadline {
                return Err(AppError::validation(
                    "截止日期不能超过3年"
                ));
            }
        }

        Ok(())
    }

    /// 验证项目工时逻辑
    pub fn validate_hours_logic(
        estimated_hours: Option<i32>,
        actual_hours: i32,
        status: &ProjectStatus,
    ) -> AppResult<()> {
        // 实际工时不能为负数
        if actual_hours < 0 {
            return Err(AppError::validation("实际工时不能为负数"));
        }

        // 如果项目已完成，实际工时应该有记录
        if *status == ProjectStatus::Completed && actual_hours == 0 {
            return Err(AppError::validation(
                "已完成的项目应该记录实际工时"
            ));
        }

        // 如果有预估工时，检查实际工时是否合理
        if let Some(estimated) = estimated_hours {
            if estimated > 0 && actual_hours > estimated * 3 {
                return Err(AppError::validation(
                    "实际工时超出预估工时过多，请检查记录是否正确"
                ));
            }
        }

        Ok(())
    }

    /// 验证领域引用
    pub async fn validate_area_reference<F, Fut>(
        area_id: Option<&str>,
        check_fn: F,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        if let Some(area_id) = area_id {
            ValidationUtils::validate_foreign_key(check_fn, "领域", area_id).await?;
        }
        Ok(())
    }

    /// 验证项目数据完整性
    pub fn validate_data_integrity(data: &CreateProjectData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 高优先级项目应该有截止日期
        if data.priority == Priority::High || data.priority == Priority::Urgent {
            if data.deadline.is_none() {
                ctx.validate(
                    || Err(AppError::validation("高优先级项目应该设置截止日期")),
                    "high_priority_deadline"
                );
            }
        }

        // 有截止日期的项目应该有预估工时
        if data.deadline.is_some() && data.estimated_hours.is_none() {
            ctx.validate(
                || Err(AppError::validation("有截止日期的项目建议设置预估工时")),
                "deadline_estimated_hours"
            );
        }

        // 项目名称不能包含特殊字符
        let invalid_chars = ['<', '>', ':', '"', '|', '?', '*'];
        for ch in invalid_chars {
            if data.name.contains(ch) {
                ctx.validate(
                    || Err(AppError::validation(&format!(
                        "项目名称不能包含特殊字符: {}",
                        ch
                    ))),
                    "name_special_chars"
                );
                break;
            }
        }

        ctx.finish()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_project_validation() {
        let validator = CreateProjectValidator;
        
        // 有效数据
        let valid_data = CreateProjectData {
            name: "Test Project".to_string(),
            description: Some("A test project".to_string()),
            priority: Priority::Medium,
            start_date: None,
            deadline: None,
            estimated_hours: Some(40),
            area_id: Some("area123".to_string()),
            created_by: "user123".to_string(),
        };
        
        assert!(validator.validate(&valid_data).is_ok());

        // 无效名称
        let invalid_data = CreateProjectData {
            name: "".to_string(),
            ..valid_data.clone()
        };
        assert!(validator.validate(&invalid_data).is_err());

        // 无效工时
        let invalid_data = CreateProjectData {
            estimated_hours: Some(-10),
            ..valid_data.clone()
        };
        assert!(validator.validate(&invalid_data).is_err());
    }

    #[test]
    fn test_status_transition_validation() {
        use ProjectStatus::*;

        // 有效转换
        assert!(ProjectBusinessRuleValidator::validate_status_transition(
            &NotStarted, &InProgress
        ).is_ok());
        
        assert!(ProjectBusinessRuleValidator::validate_status_transition(
            &InProgress, &Completed
        ).is_ok());

        // 无效转换
        assert!(ProjectBusinessRuleValidator::validate_status_transition(
            &Completed, &InProgress
        ).is_err());
        
        assert!(ProjectBusinessRuleValidator::validate_status_transition(
            &Archived, &InProgress
        ).is_err());
    }

    #[test]
    fn test_completion_requirements() {
        // 有效完成
        assert!(ProjectBusinessRuleValidator::validate_completion_requirements(
            100, &ProjectStatus::Completed, false
        ).is_ok());

        // 进度不足
        assert!(ProjectBusinessRuleValidator::validate_completion_requirements(
            80, &ProjectStatus::Completed, false
        ).is_err());

        // 有未完成任务
        assert!(ProjectBusinessRuleValidator::validate_completion_requirements(
            100, &ProjectStatus::Completed, true
        ).is_err());
    }

    #[test]
    fn test_deadline_constraints() {
        let today = chrono::Utc::now().date_naive();
        let future_date = today + chrono::Duration::days(30);
        let past_date = today - chrono::Duration::days(30);

        // 有效截止日期
        assert!(ProjectBusinessRuleValidator::validate_deadline_constraints(
            Some(future_date), None, &ProjectStatus::InProgress
        ).is_ok());

        // 过去的截止日期（未开始项目）
        assert!(ProjectBusinessRuleValidator::validate_deadline_constraints(
            Some(past_date), None, &ProjectStatus::NotStarted
        ).is_err());
    }
}
