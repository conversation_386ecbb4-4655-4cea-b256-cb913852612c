{"name": "pao<PERSON>", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "biome check .", "lint:fix": "biome check --apply .", "format": "biome format --write .", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite src-tauri/target", "deps:update": "pnpm update --latest"}, "license": "MIT", "dependencies": {"@kobalte/core": "^0.13.0", "@solid-primitives/intersection-observer": "^2.1.0", "@solid-primitives/resize-observer": "^2.0.0", "@solidjs/router": "^0.15.3", "@tabler/icons-solidjs": "^3.0.0", "@tanstack/solid-query": "^5.0.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-clipboard-manager": "^2", "@tauri-apps/plugin-dialog": "^2", "@tauri-apps/plugin-fs": "^2", "@tauri-apps/plugin-global-shortcut": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2", "@tauri-apps/plugin-store": "^2", "@tauri-apps/plugin-updater": "^2", "@tauri-apps/plugin-window-state": "^2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.0", "fuse.js": "^7.0.0", "lucide-solid": "^0.542.0", "solid-js": "^1.9.3", "solid-toast": "^0.5.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@biomejs/biome": "^1.9.0", "@solidjs/testing-library": "^0.8.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^2", "@types/node": "^22.0.0", "@vitest/ui": "^3.0.0", "autoprefixer": "^10.4.21", "jsdom": "^25.0.0", "typescript": "~5.6.2", "vite": "^6.0.3", "vite-plugin-solid": "^2.11.0", "vitest": "^3.0.0"}}