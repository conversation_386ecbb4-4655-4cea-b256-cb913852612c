import { createContext, useContext, createSignal, createEffect, ParentComponent } from 'solid-js';
import { invoke } from '@tauri-apps/api/core';
import toast from 'solid-toast';

// 类型定义
export interface User {
  id: string;
  username: string;
  email?: string;
  full_name?: string;
  timezone: string;
  language: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email?: string;
  password: string;
  full_name?: string;
  timezone?: string;
  language?: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  expires_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  request_id?: string;
}

// 认证上下文类型
interface AuthContextType {
  user: () => User | null;
  isAuthenticated: () => boolean;
  isLoading: () => boolean;
  login: (credentials: LoginRequest) => Promise<boolean>;
  register: (userData: RegisterRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

// 创建上下文
const AuthContext = createContext<AuthContextType>();

// 认证提供者组件
export const AuthProvider: ParentComponent = (props) => {
  const [user, setUser] = createSignal<User | null>(null);
  const [isLoading, setIsLoading] = createSignal(true);
  const [token, setToken] = createSignal<string | null>(null);

  // 计算属性
  const isAuthenticated = () => user() !== null && token() !== null;

  // 初始化认证状态
  createEffect(async () => {
    try {
      const savedToken = localStorage.getItem('auth_token');
      const savedUser = localStorage.getItem('auth_user');

      if (savedToken && savedUser) {
        setToken(savedToken);
        setUser(JSON.parse(savedUser));

        // 在开发阶段，暂时跳过token验证以避免阻塞
        // TODO: 在生产环境中启用token验证
        // const isValid = await validateToken(savedToken);
        // if (!isValid) {
        //   await logout();
        // }
      }
    } catch (error) {
      console.error('Failed to initialize auth state:', error);
      // 在开发阶段，不要因为认证错误而阻塞应用
      // await logout();
    } finally {
      setIsLoading(false);
    }
  });

  // 验证token
  const validateToken = async (tokenToValidate: string): Promise<boolean> => {
    try {
      const response = await invoke<ApiResponse<boolean>>('validate_token', {
        token: tokenToValidate
      });
      return response.success && response.data === true;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  };

  // 登录
  const login = async (credentials: LoginRequest): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      const response = await invoke<ApiResponse<LoginResponse>>('login', {
        request: credentials
      });

      if (response.success && response.data) {
        const { user: userData, token: userToken } = response.data;
        
        setUser(userData);
        setToken(userToken);
        
        // 保存到本地存储
        localStorage.setItem('auth_token', userToken);
        localStorage.setItem('auth_user', JSON.stringify(userData));
        
        toast.success('登录成功！');
        return true;
      } else {
        toast.error(response.error || '登录失败');
        return false;
      }
    } catch (error) {
      console.error('Login failed:', error);
      toast.error('登录失败，请稍后重试');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 注册
  const register = async (userData: RegisterRequest): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      const response = await invoke<ApiResponse<User>>('register', {
        request: userData
      });

      if (response.success && response.data) {
        toast.success('注册成功！请登录');
        return true;
      } else {
        toast.error(response.error || '注册失败');
        return false;
      }
    } catch (error) {
      console.error('Registration failed:', error);
      toast.error('注册失败，请稍后重试');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 登出
  const logout = async (): Promise<void> => {
    try {
      const currentUserId = user()?.id;
      
      if (currentUserId) {
        await invoke<ApiResponse<void>>('logout', {
          currentUserId
        });
      }
      
      // 清除状态
      setUser(null);
      setToken(null);
      
      // 清除本地存储
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      
      toast.success('已退出登录');
    } catch (error) {
      console.error('Logout failed:', error);
      // 即使后端调用失败，也要清除本地状态
      setUser(null);
      setToken(null);
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
    }
  };

  // 刷新用户信息
  const refreshUser = async (): Promise<void> => {
    try {
      const currentUserId = user()?.id;
      if (!currentUserId) return;

      const response = await invoke<ApiResponse<User | null>>('get_current_user', {
        currentUserId
      });

      if (response.success && response.data) {
        setUser(response.data);
        localStorage.setItem('auth_user', JSON.stringify(response.data));
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {props.children}
    </AuthContext.Provider>
  );
};

// 使用认证上下文的Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// 获取当前用户ID的便捷函数
export const getCurrentUserId = (): string | null => {
  const { user } = useAuth();
  return user()?.id || null;
};
