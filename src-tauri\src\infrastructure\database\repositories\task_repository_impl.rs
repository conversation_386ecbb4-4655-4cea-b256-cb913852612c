// 任务仓储实现
// 实现任务数据访问的具体逻辑

use crate::domain::entities::task::{Task, CreateTaskData, UpdateTaskData, TaskQuery, TaskStats};
use crate::domain::repositories::task_repository::{
    TaskRepository, TaskHierarchy, ProjectTaskStats, AreaTaskStats, UserTaskStats,
    TaskDependency, DependencyType
};
use crate::infrastructure::database::models::TaskModel;
use crate::infrastructure::database::mappers::{task_mapper as TaskMapper, Mapper};
use crate::infrastructure::database::DatabaseUtils;
use crate::shared::types::{EntityId, QueryParams, TaskStatus, Priority};
use crate::shared::errors::{AppError, AppResult};
use async_trait::async_trait;
use sqlx::{SqlitePool, Row};
use chrono::{Utc, NaiveDate};

/// 任务仓储实现
pub struct TaskRepositoryImpl {
    pool: SqlitePool,
}

impl TaskRepositoryImpl {
    /// 创建新的任务仓储实现
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl TaskRepository for TaskRepositoryImpl {
    async fn create(&self, data: CreateTaskData) -> AppResult<Task> {
        // 验证父任务存在（如果指定了父任务）
        if let Some(ref parent_id) = data.parent_task_id {
            if self.find_by_id(parent_id).await?.is_none() {
                return Err(AppError::validation("父任务不存在"));
            }
        }

        // 创建任务实体
        let task = Task::new(data)?;
        let model = TaskMapper::to_model(&task);

        // 插入数据库
        sqlx::query!(
            r#"
            INSERT INTO tasks (
                id, title, description, status, priority, parent_task_id,
                project_id, area_id, assigned_to, due_date, estimated_minutes,
                actual_minutes, completion_percentage, sort_order,
                created_at, updated_at, completed_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            model.id,
            model.title,
            model.description,
            model.status,
            model.priority,
            model.parent_task_id,
            model.project_id,
            model.area_id,
            model.assigned_to,
            model.due_date,
            model.estimated_minutes,
            model.actual_minutes,
            model.completion_percentage,
            model.sort_order,
            model.created_at,
            model.updated_at,
            model.completed_at
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to create task: {}", e)))?;

        Ok(task)
    }

    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<Task>> {
        let model = sqlx::query_as!(
            TaskModel,
            "SELECT * FROM tasks WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find task by id: {}", e)))?;

        match model {
            Some(model) => Ok(Some(TaskMapper::to_domain(&model)?)),
            None => Ok(None),
        }
    }

    async fn update(&self, id: &EntityId, data: UpdateTaskData) -> AppResult<Task> {
        // 先获取现有任务
        let mut task = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        // 验证父任务（如果要更新父任务）
        if let Some(ref parent_id) = data.parent_task_id {
            if parent_id == id {
                return Err(AppError::validation("任务不能设置自己为父任务"));
            }
            if self.find_by_id(parent_id).await?.is_none() {
                return Err(AppError::validation("父任务不存在"));
            }
        }

        // 更新任务实体
        task.update(data)?;
        let model = TaskMapper::to_model(&task);

        // 更新数据库
        sqlx::query!(
            r#"
            UPDATE tasks SET
                title = ?, description = ?, status = ?, priority = ?, parent_task_id = ?,
                project_id = ?, area_id = ?, assigned_to = ?, due_date = ?, estimated_minutes = ?,
                completion_percentage = ?, sort_order = ?, updated_at = ?, completed_at = ?
            WHERE id = ?
            "#,
            model.title,
            model.description,
            model.status,
            model.priority,
            model.parent_task_id,
            model.project_id,
            model.area_id,
            model.assigned_to,
            model.due_date,
            model.estimated_minutes,
            model.completion_percentage,
            model.sort_order,
            model.updated_at,
            model.completed_at,
            model.id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update task: {}", e)))?;

        Ok(task)
    }

    async fn delete(&self, id: &EntityId) -> AppResult<()> {
        // 检查是否有子任务
        let children = self.find_by_parent_id(id, QueryParams::default()).await?;
        if !children.is_empty() {
            return Err(AppError::business_logic("不能删除有子任务的任务"));
        }

        let result = sqlx::query!(
            "DELETE FROM tasks WHERE id = ?",
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to delete task: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("任务"));
        }

        Ok(())
    }

    async fn find_all(&self, query: TaskQuery, params: QueryParams) -> AppResult<Vec<Task>> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件
        if let Some(title) = &query.title {
            conditions.push("title LIKE ?".to_string());
            query_params.push(format!("%{}%", title));
        }

        if let Some(status) = &query.status {
            conditions.push("status = ?".to_string());
            query_params.push(status.to_string());
        }

        if let Some(priority) = &query.priority {
            conditions.push("priority = ?".to_string());
            query_params.push(priority.to_i32().to_string());
        }

        if let Some(parent_task_id) = &query.parent_task_id {
            conditions.push("parent_task_id = ?".to_string());
            query_params.push(parent_task_id.clone());
        }

        if let Some(project_id) = &query.project_id {
            conditions.push("project_id = ?".to_string());
            query_params.push(project_id.clone());
        }

        if let Some(area_id) = &query.area_id {
            conditions.push("area_id = ?".to_string());
            query_params.push(area_id.clone());
        }

        if let Some(assigned_to) = &query.assigned_to {
            conditions.push("assigned_to = ?".to_string());
            query_params.push(assigned_to.clone());
        }

        if let Some(due_before) = query.due_before {
            conditions.push("due_date <= ?".to_string());
            query_params.push(due_before.to_string());
        }

        if let Some(due_after) = query.due_after {
            conditions.push("due_date >= ?".to_string());
            query_params.push(due_after.to_string());
        }

        if let Some(created_after) = query.created_after {
            conditions.push("created_at >= ?".to_string());
            query_params.push(created_after.to_rfc3339());
        }

        if let Some(created_before) = query.created_before {
            conditions.push("created_at <= ?".to_string());
            query_params.push(created_before.to_rfc3339());
        }

        // 添加搜索条件
        if let Some(search) = &params.search {
            let search_condition = DatabaseUtils::build_search_condition(search, &["title", "description"]);
            if !search_condition.is_empty() {
                conditions.push(search_condition);
            }
        }

        // 构建完整查询
        let base_query = "SELECT * FROM tasks";
        let sorts = params.sort.as_deref().unwrap_or(&[]);
        let sql = DatabaseUtils::build_query(base_query, conditions, sorts, &params.pagination);

        // 执行查询
        let mut query_builder = sqlx::query_as::<_, TaskModel>(&sql);
        for param in query_params {
            query_builder = query_builder.bind(param);
        }

        let models = query_builder
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to find tasks: {}", e)))?;

        TaskMapper::to_domain_list(models)
    }

    async fn count(&self, query: TaskQuery) -> AppResult<u64> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件（与find_all相同的逻辑）
        if let Some(title) = &query.title {
            conditions.push("title LIKE ?".to_string());
            query_params.push(format!("%{}%", title));
        }

        if let Some(status) = &query.status {
            conditions.push("status = ?".to_string());
            query_params.push(status.to_string());
        }

        if let Some(priority) = &query.priority {
            conditions.push("priority = ?".to_string());
            query_params.push(priority.to_i32().to_string());
        }

        if let Some(parent_task_id) = &query.parent_task_id {
            conditions.push("parent_task_id = ?".to_string());
            query_params.push(parent_task_id.clone());
        }

        if let Some(project_id) = &query.project_id {
            conditions.push("project_id = ?".to_string());
            query_params.push(project_id.clone());
        }

        if let Some(area_id) = &query.area_id {
            conditions.push("area_id = ?".to_string());
            query_params.push(area_id.clone());
        }

        if let Some(assigned_to) = &query.assigned_to {
            conditions.push("assigned_to = ?".to_string());
            query_params.push(assigned_to.clone());
        }

        let base_query = "SELECT COUNT(*) FROM tasks";
        let sql = format!("{}{}", base_query, DatabaseUtils::build_where_clause(conditions));

        let mut query_builder = sqlx::query(&sql);
        for param in query_params {
            query_builder = query_builder.bind(param);
        }

        let row = query_builder
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count tasks: {}", e)))?;

        let count: i64 = row.get(0);
        Ok(count as u64)
    }

    async fn find_by_project_id(&self, project_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>> {
        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: Some(project_id.clone()),
            area_id: None,
            assigned_to: None,
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_area_id(&self, area_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>> {
        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: Some(area_id.clone()),
            assigned_to: None,
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_parent_id(&self, parent_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>> {
        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: Some(parent_id.clone()),
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_assignee(&self, assignee_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>> {
        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: Some(assignee_id.clone()),
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_status(&self, status: TaskStatus, params: QueryParams) -> AppResult<Vec<Task>> {
        let query = TaskQuery {
            title: None,
            status: Some(status),
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_priority(&self, priority: Priority, params: QueryParams) -> AppResult<Vec<Task>> {
        let query = TaskQuery {
            title: None,
            status: None,
            priority: Some(priority),
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_root_tasks(&self, params: QueryParams) -> AppResult<Vec<Task>> {
        let models = sqlx::query_as!(
            TaskModel,
            r#"
            SELECT * FROM tasks 
            WHERE parent_task_id IS NULL
            ORDER BY sort_order ASC, created_at ASC
            LIMIT ? OFFSET ?
            "#,
            params.pagination.as_ref().map(|p| p.limit()).unwrap_or(50),
            params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find root tasks: {}", e)))?;

        TaskMapper::to_domain_list(models)
    }

    async fn find_overdue(&self, params: QueryParams) -> AppResult<Vec<Task>> {
        let today = Utc::now().date_naive();
        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_before: Some(today),
            due_after: None,
            created_after: None,
            created_before: None,
        };

        // 只查找活跃的任务
        let mut tasks = self.find_all(query, params).await?;
        tasks.retain(|t| t.status.is_active());
        Ok(tasks)
    }

    async fn find_due_soon(&self, days: i32, params: QueryParams) -> AppResult<Vec<Task>> {
        let today = Utc::now().date_naive();
        let due_date = today + chrono::Duration::days(days as i64);
        
        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_before: Some(due_date),
            due_after: Some(today),
            created_after: None,
            created_before: None,
        };

        // 只查找活跃的任务
        let mut tasks = self.find_all(query, params).await?;
        tasks.retain(|t| t.status.is_active());
        Ok(tasks)
    }

    async fn find_active(&self, params: QueryParams) -> AppResult<Vec<Task>> {
        let models = sqlx::query_as!(
            TaskModel,
            r#"
            SELECT * FROM tasks 
            WHERE status IN ('todo', 'in_progress', 'waiting')
            ORDER BY priority DESC, due_date ASC, sort_order ASC
            LIMIT ? OFFSET ?
            "#,
            params.pagination.as_ref().map(|p| p.limit()).unwrap_or(50),
            params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find active tasks: {}", e)))?;

        TaskMapper::to_domain_list(models)
    }

    async fn find_completed(&self, params: QueryParams) -> AppResult<Vec<Task>> {
        let models = sqlx::query_as!(
            TaskModel,
            r#"
            SELECT * FROM tasks 
            WHERE status = 'completed'
            ORDER BY completed_at DESC
            LIMIT ? OFFSET ?
            "#,
            params.pagination.as_ref().map(|p| p.limit()).unwrap_or(50),
            params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find completed tasks: {}", e)))?;

        TaskMapper::to_domain_list(models)
    }

    async fn get_task_hierarchy(&self, root_task_id: Option<&EntityId>) -> AppResult<Vec<TaskHierarchy>> {
        // 使用递归CTE查询任务层级结构
        let sql = if let Some(root_id) = root_task_id {
            format!(
                r#"
                WITH RECURSIVE task_tree AS (
                    SELECT *, 0 as level, CAST(sort_order AS TEXT) as path
                    FROM tasks 
                    WHERE id = '{}'
                    
                    UNION ALL
                    
                    SELECT t.*, tt.level + 1, tt.path || '.' || CAST(t.sort_order AS TEXT)
                    FROM tasks t
                    JOIN task_tree tt ON t.parent_task_id = tt.id
                )
                SELECT * FROM task_tree ORDER BY path
                "#,
                root_id
            )
        } else {
            r#"
            WITH RECURSIVE task_tree AS (
                SELECT *, 0 as level, CAST(sort_order AS TEXT) as path
                FROM tasks 
                WHERE parent_task_id IS NULL
                
                UNION ALL
                
                SELECT t.*, tt.level + 1, tt.path || '.' || CAST(t.sort_order AS TEXT)
                FROM tasks t
                JOIN task_tree tt ON t.parent_task_id = tt.id
            )
            SELECT * FROM task_tree ORDER BY path
            "#.to_string()
        };

        let rows = sqlx::query(&sql)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get task hierarchy: {}", e)))?;

        let mut hierarchies = Vec::new();
        for row in rows {
            let model = TaskModel {
                id: row.get("id"),
                title: row.get("title"),
                description: row.get("description"),
                status: row.get("status"),
                priority: row.get("priority"),
                parent_task_id: row.get("parent_task_id"),
                project_id: row.get("project_id"),
                area_id: row.get("area_id"),
                assigned_to: row.get("assigned_to"),
                due_date: row.get("due_date"),
                estimated_minutes: row.get("estimated_minutes"),
                actual_minutes: row.get("actual_minutes"),
                completion_percentage: row.get("completion_percentage"),
                sort_order: row.get("sort_order"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
                completed_at: row.get("completed_at"),
            };

            let task = TaskMapper::to_domain(&model)?;
            let level: i32 = row.get("level");
            let path: String = row.get("path");

            hierarchies.push(TaskHierarchy {
                task,
                level: level as u32,
                path,
                children: Vec::new(), // 子任务将在后续处理中填充
            });
        }

        Ok(hierarchies)
    }

    async fn update_status(&self, id: &EntityId, status: TaskStatus) -> AppResult<()> {
        let completed_at = if status == TaskStatus::Completed {
            Some(Utc::now())
        } else {
            None
        };

        let result = sqlx::query!(
            "UPDATE tasks SET status = ?, completed_at = ?, updated_at = ? WHERE id = ?",
            status.to_string(),
            completed_at,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update task status: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("任务"));
        }

        Ok(())
    }

    async fn update_completion_percentage(&self, id: &EntityId, percentage: u8) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE tasks SET completion_percentage = ?, updated_at = ? WHERE id = ?",
            percentage as i32,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update task completion percentage: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("任务"));
        }

        Ok(())
    }

    async fn update_sort_order(&self, id: &EntityId, sort_order: i32) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE tasks SET sort_order = ?, updated_at = ? WHERE id = ?",
            sort_order,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update task sort order: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("任务"));
        }

        Ok(())
    }

    async fn batch_update_sort_order(&self, updates: Vec<(EntityId, i32)>) -> AppResult<()> {
        if updates.is_empty() {
            return Ok(());
        }

        let mut tx = self.pool.begin().await
            .map_err(|e| AppError::database(format!("Failed to begin transaction: {}", e)))?;

        for (id, sort_order) in updates {
            sqlx::query!(
                "UPDATE tasks SET sort_order = ?, updated_at = ? WHERE id = ?",
                sort_order,
                Utc::now(),
                id
            )
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::database(format!("Failed to update task sort order: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| AppError::database(format!("Failed to commit transaction: {}", e)))?;

        Ok(())
    }

    async fn add_work_time(&self, id: &EntityId, minutes: u32) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE tasks SET actual_minutes = actual_minutes + ?, updated_at = ? WHERE id = ?",
            minutes as i32,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to add work time: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("任务"));
        }

        Ok(())
    }

    async fn move_task(&self, id: &EntityId, new_parent_id: Option<&EntityId>) -> AppResult<()> {
        // 验证新父任务存在（如果指定了）
        if let Some(parent_id) = new_parent_id {
            if parent_id == id {
                return Err(AppError::validation("任务不能设置自己为父任务"));
            }
            if self.find_by_id(parent_id).await?.is_none() {
                return Err(AppError::validation("父任务不存在"));
            }
        }

        let result = sqlx::query!(
            "UPDATE tasks SET parent_task_id = ?, updated_at = ? WHERE id = ?",
            new_parent_id,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to move task: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("任务"));
        }

        Ok(())
    }

    async fn copy_task(&self, id: &EntityId, new_parent_id: Option<&EntityId>) -> AppResult<Task> {
        // 获取原任务
        let original_task = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        // 创建新任务数据
        let create_data = CreateTaskData {
            title: format!("{} (副本)", original_task.title),
            description: original_task.description,
            priority: Some(original_task.priority),
            parent_task_id: new_parent_id.map(|id| id.clone()),
            project_id: original_task.project_id,
            area_id: original_task.area_id,
            assigned_to: original_task.assigned_to,
            due_date: original_task.due_date,
            estimated_minutes: original_task.estimated_minutes,
        };

        self.create(create_data).await
    }

    async fn get_stats(&self, id: &EntityId) -> AppResult<TaskStats> {
        let task = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        Ok(task.get_stats())
    }

    async fn get_project_task_stats(&self, project_id: &EntityId) -> AppResult<ProjectTaskStats> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_tasks,
                COUNT(CASE WHEN status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as active_tasks,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
                COUNT(CASE WHEN due_date < date('now') AND status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as overdue_tasks,
                AVG(completion_percentage) as average_completion_percentage
            FROM tasks 
            WHERE project_id = ?
            "#,
            project_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get project task stats: {}", e)))?;

        let total = row.total_tasks as u64;
        let completed = row.completed_tasks as u64;
        let completion_rate = if total > 0 { completed as f64 / total as f64 } else { 0.0 };

        Ok(ProjectTaskStats {
            total_tasks: total,
            active_tasks: row.active_tasks as u64,
            completed_tasks: completed,
            overdue_tasks: row.overdue_tasks as u64,
            completion_rate,
            average_completion_percentage: row.average_completion_percentage.unwrap_or(0.0),
        })
    }

    async fn get_area_task_stats(&self, area_id: &EntityId) -> AppResult<AreaTaskStats> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_tasks,
                COUNT(CASE WHEN status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as active_tasks,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
                COUNT(CASE WHEN due_date < date('now') AND status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as overdue_tasks,
                AVG(completion_percentage) as average_completion_percentage
            FROM tasks 
            WHERE area_id = ?
            "#,
            area_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get area task stats: {}", e)))?;

        let total = row.total_tasks as u64;
        let completed = row.completed_tasks as u64;
        let completion_rate = if total > 0 { completed as f64 / total as f64 } else { 0.0 };

        Ok(AreaTaskStats {
            total_tasks: total,
            active_tasks: row.active_tasks as u64,
            completed_tasks: completed,
            overdue_tasks: row.overdue_tasks as u64,
            completion_rate,
            average_completion_percentage: row.average_completion_percentage.unwrap_or(0.0),
        })
    }

    async fn get_user_task_stats(&self, user_id: &EntityId) -> AppResult<UserTaskStats> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_assigned_tasks,
                COUNT(CASE WHEN status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as active_assigned_tasks,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_assigned_tasks,
                COUNT(CASE WHEN due_date < date('now') AND status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as overdue_assigned_tasks,
                AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL 
                    THEN julianday(completed_at) - julianday(created_at) END) as average_completion_time_days
            FROM tasks 
            WHERE assigned_to = ?
            "#,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get user task stats: {}", e)))?;

        let total = row.total_assigned_tasks as u64;
        let completed = row.completed_assigned_tasks as u64;
        let completion_rate = if total > 0 { completed as f64 / total as f64 } else { 0.0 };

        Ok(UserTaskStats {
            total_assigned_tasks: total,
            active_assigned_tasks: row.active_assigned_tasks as u64,
            completed_assigned_tasks: completed,
            overdue_assigned_tasks: row.overdue_assigned_tasks as u64,
            completion_rate,
            average_completion_time_days: row.average_completion_time_days,
        })
    }

    async fn batch_update_status(&self, ids: Vec<EntityId>, status: TaskStatus) -> AppResult<u64> {
        if ids.is_empty() {
            return Ok(0);
        }

        let completed_at = if status == TaskStatus::Completed {
            Some(Utc::now())
        } else {
            None
        };

        let placeholders = ids.iter().map(|_| "?").collect::<Vec<_>>().join(",");
        let sql = format!(
            "UPDATE tasks SET status = ?, completed_at = ?, updated_at = ? WHERE id IN ({})",
            placeholders
        );

        let mut query = sqlx::query(&sql)
            .bind(status.to_string())
            .bind(completed_at)
            .bind(Utc::now());

        for id in ids {
            query = query.bind(id);
        }

        let result = query
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to batch update task status: {}", e)))?;

        Ok(result.rows_affected())
    }

    async fn search(&self, keyword: &str, params: QueryParams) -> AppResult<Vec<Task>> {
        let search_params = QueryParams {
            search: Some(keyword.to_string()),
            ..params
        };

        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, search_params).await
    }

    async fn get_task_dependencies(&self, _id: &EntityId) -> AppResult<Vec<TaskDependency>> {
        // 任务依赖功能暂未实现，返回空列表
        Ok(Vec::new())
    }

    async fn add_dependency(&self, _task_id: &EntityId, _depends_on_id: &EntityId) -> AppResult<()> {
        // 任务依赖功能暂未实现
        Err(AppError::not_implemented("任务依赖功能暂未实现"))
    }

    async fn remove_dependency(&self, _task_id: &EntityId, _depends_on_id: &EntityId) -> AppResult<()> {
        // 任务依赖功能暂未实现
        Err(AppError::not_implemented("任务依赖功能暂未实现"))
    }
}
