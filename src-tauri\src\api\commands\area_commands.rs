// 领域相关的Tauri命令
// 实现领域管理的前后端通信

use crate::api::{ApiResponse, ApiState, PaginationRequest, CommandUtils};
use crate::domain::entities::area::{CreateAreaData, UpdateAreaData, AreaQuery};
use crate::execute_authenticated_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};

/// 创建领域请求
#[derive(Debug, Deserialize)]
pub struct CreateAreaRequest {
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: Option<i32>,
}

/// 更新领域请求
#[derive(Debug, Deserialize)]
pub struct UpdateAreaRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: Option<i32>,
    pub is_active: Option<bool>,
}

/// 领域响应
#[derive(Debug, Serialize)]
pub struct AreaResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: i32,
    pub is_active: bool,
    pub created_by: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub archived_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl From<crate::domain::entities::area::Area> for AreaResponse {
    fn from(area: crate::domain::entities::area::Area) -> Self {
        Self {
            id: area.id,
            name: area.name,
            description: area.description,
            standard: area.standard,
            icon_name: area.icon_name,
            color_hex: area.color_hex,
            sort_order: area.sort_order,
            is_active: area.is_active,
            created_by: area.created_by,
            created_at: area.created_at,
            updated_at: area.updated_at,
            archived_at: area.archived_at,
        }
    }
}

/// 领域摘要响应
#[derive(Debug, Serialize)]
pub struct AreaSummaryResponse {
    pub area: AreaResponse,
    pub total_projects: u64,
    pub completed_projects: u64,
    pub active_projects: u64,
    pub total_tasks: u64,
    pub completed_tasks: u64,
    pub active_tasks: u64,
    pub total_habits: u64,
    pub active_habits: u64,
}

impl From<crate::domain::repositories::area_repository::AreaSummary> for AreaSummaryResponse {
    fn from(summary: crate::domain::repositories::area_repository::AreaSummary) -> Self {
        Self {
            area: AreaResponse::from(summary.area),
            total_projects: summary.total_projects,
            completed_projects: summary.completed_projects,
            active_projects: summary.active_projects,
            total_tasks: summary.total_tasks,
            completed_tasks: summary.completed_tasks,
            active_tasks: summary.active_tasks,
            total_habits: summary.total_habits,
            active_habits: summary.active_habits,
        }
    }
}

/// 创建领域
#[command]
pub async fn create_area(
    request: CreateAreaRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<AreaResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let create_data = CreateAreaData {
        name: request.name,
        description: request.description,
        standard: request.standard,
        icon_name: request.icon_name,
        color_hex: request.color_hex,
        sort_order: request.sort_order,
        created_by: current_user_id.clone().unwrap_or_default(),
    };

    let response = execute_authenticated_command!(
        "create_area",
        current_user_id,
        state.area_service.create_area(&context, create_data)
    );

    Ok(response)
}

/// 获取领域
#[command]
pub async fn get_area(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Option<AreaResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_area",
        current_user_id,
        state.area_service.get_area(&context, &area_id)
    );

    Ok(response)
}

/// 更新领域
#[command]
pub async fn update_area(
    area_id: String,
    request: UpdateAreaRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<AreaResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let update_data = UpdateAreaData {
        name: request.name,
        description: request.description,
        standard: request.standard,
        icon_name: request.icon_name,
        color_hex: request.color_hex,
        sort_order: request.sort_order,
        is_active: request.is_active,
    };

    let response = execute_authenticated_command!(
        "update_area",
        current_user_id,
        state.area_service.update_area(&context, &area_id, update_data)
    );

    Ok(response)
}

/// 删除领域
#[command]
pub async fn delete_area(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "delete_area",
        current_user_id,
        state.area_service.delete_area(&context, &area_id)
    );

    Ok(response)
}

/// 列出领域
#[command]
pub async fn list_areas(
    pagination: PaginationRequest,
    active_only: Option<bool>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<AreaResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "list_areas",
        current_user_id,
        state.area_service.list_user_areas(&context, pagination.page(), pagination.page_size(), active_only.unwrap_or(false))
    );

    Ok(response)
}

/// 激活领域
#[command]
pub async fn activate_area(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "activate_area",
        current_user_id,
        state.area_service.activate_area(&context, &area_id)
    );

    Ok(response)
}

/// 停用领域
#[command]
pub async fn deactivate_area(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "deactivate_area",
        current_user_id,
        state.area_service.deactivate_area(&context, &area_id)
    );

    Ok(response)
}

/// 归档领域
#[command]
pub async fn archive_area(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "archive_area",
        current_user_id,
        state.area_service.archive_area(&context, &area_id)
    );

    Ok(response)
}

/// 获取领域摘要
#[command]
pub async fn get_area_summary(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<AreaSummaryResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_area_summary",
        current_user_id,
        state.area_service.get_area_summary(&context, &area_id)
    );

    Ok(response)
}

/// 搜索领域
#[command]
pub async fn search_areas(
    keyword: String,
    pagination: PaginationRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<AreaResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "search_areas",
        current_user_id,
        state.area_service.search_areas(&context, &keyword, pagination.page(), pagination.page_size())
    );

    Ok(response)
}
