import { Component, createSignal, onMount } from 'solid-js';
import { Router, Route } from '@solidjs/router';
import { Toaster } from 'solid-toast';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { Layout } from './components/Layout';
import { LoginPage } from './pages/auth/LoginPage';
import { RegisterPage } from './pages/auth/RegisterPage';
import { DashboardPage } from './pages/DashboardPage';
import { ProjectsPage } from './pages/projects/ProjectsPage';
import { TasksPage } from './pages/tasks/TasksPage';
import { AreasPage } from './pages/areas/AreasPage';
import { SettingsPage } from './pages/SettingsPage';
import { ProtectedRoute } from './components/ProtectedRoute';
import './App.css';

const App: Component = () => {
  const [isLoading, setIsLoading] = createSignal(true);

  onMount(async () => {
    // 初始化应用
    try {
      // 在开发阶段，直接跳过初始化延迟
      console.log('PaoLife application initializing...');
      // await new Promise(resolve => setTimeout(resolve, 1000)); // 暂时禁用延迟
    } catch (error) {
      console.error('App initialization failed:', error);
    } finally {
      setIsLoading(false);
    }
  });

  if (isLoading()) {
    return (
      <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600">正在加载 PaoLife...</p>
        </div>
      </div>
    );
  }

  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div class="min-h-screen bg-gray-50">
            {/* 公开路由 */}
            <Route path="/login" component={LoginPage} />
            <Route path="/register" component={RegisterPage} />

            {/* 受保护的路由 */}
            <Route path="/" component={() => (
              <ProtectedRoute>
                <Layout>
                  <Route path="/" component={DashboardPage} />
                  <Route path="/dashboard" component={DashboardPage} />
                  <Route path="/projects/*" component={ProjectsPage} />
                  <Route path="/tasks/*" component={TasksPage} />
                  <Route path="/areas/*" component={AreasPage} />
                  <Route path="/settings" component={SettingsPage} />
                </Layout>
              </ProtectedRoute>
            )} />

            {/* 全局通知 */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  style: {
                    background: '#10b981',
                  },
                },
                error: {
                  style: {
                    background: '#ef4444',
                  },
                },
              }}
            />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
