// 项目相关的Tauri命令
// 实现项目管理的前后端通信

use crate::api::{ApiResponse, ApiState, PaginationRequest, CommandUtils};
use crate::domain::entities::project::{CreateProjectData, UpdateProjectData, ProjectQuery};
use crate::shared::types::{ProjectStatus, Priority};
use crate::execute_authenticated_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};
use chrono::NaiveDate;

/// 创建项目请求
#[derive(Debug, Deserialize)]
pub struct CreateProjectRequest {
    pub name: String,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub start_date: Option<String>,
    pub deadline: Option<String>,
    pub estimated_hours: Option<u32>,
    pub area_id: Option<String>,
}

/// 更新项目请求
#[derive(Debug, Deserialize)]
pub struct UpdateProjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub start_date: Option<String>,
    pub deadline: Option<String>,
    pub estimated_hours: Option<u32>,
    pub area_id: Option<String>,
}

/// 项目响应
#[derive(Debug, Serialize)]
pub struct ProjectResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: String,
    pub progress: u8,
    pub priority: String,
    pub start_date: Option<String>,
    pub deadline: Option<String>,
    pub estimated_hours: Option<u32>,
    pub actual_hours: u32,
    pub area_id: Option<String>,
    pub created_by: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

impl From<crate::domain::entities::project::Project> for ProjectResponse {
    fn from(project: crate::domain::entities::project::Project) -> Self {
        Self {
            id: project.id,
            name: project.name,
            description: project.description,
            status: project.status.to_string(),
            progress: project.progress,
            priority: project.priority.to_string(),
            start_date: project.start_date.map(|d| d.to_string()),
            deadline: project.deadline.map(|d| d.to_string()),
            estimated_hours: project.estimated_hours,
            actual_hours: project.actual_hours,
            area_id: project.area_id,
            created_by: project.created_by,
            created_at: project.created_at,
            updated_at: project.updated_at,
        }
    }
}

/// 创建项目
#[command]
pub async fn create_project(
    request: CreateProjectRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<ProjectResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let priority = request.priority.as_deref()
        .and_then(|p| match p {
            "low" => Some(Priority::Low),
            "medium" => Some(Priority::Medium),
            "high" => Some(Priority::High),
            "urgent" => Some(Priority::Urgent),
            _ => None,
        })
        .unwrap_or(Priority::Medium);

    let start_date = request.start_date
        .and_then(|d| NaiveDate::parse_from_str(&d, "%Y-%m-%d").ok());
    
    let deadline = request.deadline
        .and_then(|d| NaiveDate::parse_from_str(&d, "%Y-%m-%d").ok());

    let create_data = CreateProjectData {
        name: request.name,
        description: request.description,
        priority: Some(priority),
        start_date,
        deadline,
        estimated_hours: request.estimated_hours,
        area_id: request.area_id,
        created_by: current_user_id.clone().unwrap_or_default(),
    };

    let response = execute_authenticated_command!(
        "create_project",
        current_user_id,
        state.project_service.create_project(&context, create_data)
    );

    Ok(response)
}

/// 获取项目
#[command]
pub async fn get_project(
    project_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Option<ProjectResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_project",
        current_user_id,
        state.project_service.get_project(&context, &project_id)
    );

    Ok(response)
}

/// 更新项目
#[command]
pub async fn update_project(
    project_id: String,
    request: UpdateProjectRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<ProjectResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let priority = request.priority.as_deref()
        .and_then(|p| match p {
            "low" => Some(Priority::Low),
            "medium" => Some(Priority::Medium),
            "high" => Some(Priority::High),
            "urgent" => Some(Priority::Urgent),
            _ => None,
        });

    let start_date = request.start_date
        .and_then(|d| NaiveDate::parse_from_str(&d, "%Y-%m-%d").ok());
    
    let deadline = request.deadline
        .and_then(|d| NaiveDate::parse_from_str(&d, "%Y-%m-%d").ok());

    let update_data = UpdateProjectData {
        name: request.name,
        description: request.description,
        priority,
        start_date,
        deadline,
        estimated_hours: request.estimated_hours,
        area_id: request.area_id,
    };

    let response = execute_authenticated_command!(
        "update_project",
        current_user_id,
        state.project_service.update_project(&context, &project_id, update_data)
    );

    Ok(response)
}

/// 删除项目
#[command]
pub async fn delete_project(
    project_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "delete_project",
        current_user_id,
        state.project_service.delete_project(&context, &project_id)
    );

    Ok(response)
}

/// 列出项目
#[command]
pub async fn list_projects(
    pagination: PaginationRequest,
    status_filter: Option<String>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<ProjectResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let status = status_filter.as_deref()
        .and_then(|s| match s {
            "not_started" => Some(ProjectStatus::NotStarted),
            "in_progress" => Some(ProjectStatus::InProgress),
            "at_risk" => Some(ProjectStatus::AtRisk),
            "paused" => Some(ProjectStatus::Paused),
            "completed" => Some(ProjectStatus::Completed),
            "archived" => Some(ProjectStatus::Archived),
            _ => None,
        });

    let response = execute_authenticated_command!(
        "list_projects",
        current_user_id,
        state.project_service.list_user_projects(&context, pagination.page(), pagination.page_size(), status)
    );

    Ok(response)
}

/// 更新项目状态
#[command]
pub async fn update_project_status(
    project_id: String,
    status: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let project_status = match status.as_str() {
        "not_started" => ProjectStatus::NotStarted,
        "in_progress" => ProjectStatus::InProgress,
        "at_risk" => ProjectStatus::AtRisk,
        "paused" => ProjectStatus::Paused,
        "completed" => ProjectStatus::Completed,
        "archived" => ProjectStatus::Archived,
        _ => return Ok(ApiResponse::error("无效的项目状态".to_string())),
    };

    let response = execute_authenticated_command!(
        "update_project_status",
        current_user_id,
        state.project_service.update_project_status(&context, &project_id, project_status)
    );

    Ok(response)
}

/// 更新项目进度
#[command]
pub async fn update_project_progress(
    project_id: String,
    progress: u8,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "update_project_progress",
        current_user_id,
        state.project_service.update_project_progress(&context, &project_id, progress)
    );

    Ok(response)
}

/// 获取逾期项目
#[command]
pub async fn get_overdue_projects(
    pagination: PaginationRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<ProjectResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_overdue_projects",
        current_user_id,
        state.project_service.get_overdue_projects(&context, pagination.page(), pagination.page_size())
    );

    Ok(response)
}

/// 获取项目统计
#[command]
pub async fn get_project_stats(
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::domain::repositories::project_repository::UserProjectStats, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_project_stats",
        current_user_id,
        state.project_service.get_user_project_stats(&context)
    );

    Ok(response)
}

/// 归档项目
#[command]
pub async fn archive_project(
    project_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "archive_project",
        current_user_id,
        state.project_service.archive_project(&context, &project_id)
    );

    Ok(response)
}

/// 搜索项目
#[command]
pub async fn search_projects(
    keyword: String,
    pagination: PaginationRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<ProjectResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "search_projects",
        current_user_id,
        state.project_service.search_projects(&context, &keyword, pagination.page(), pagination.page_size())
    );

    Ok(response)
}
