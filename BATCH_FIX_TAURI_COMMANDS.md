# Tauri 命令返回类型批量修复脚本

## 🎯 修复目标

将所有 Tauri 命令的返回类型从 `Result<ApiResponse<T>, ()>` 修改为 `Result<T, String>`，以符合 Tauri 的要求。

## 📋 需要修复的文件和模式

### 1. 通用修复模式

**查找模式**:
```regex
-> Result<ApiResponse<(.+?)>, \(\)>
```

**替换为**:
```regex
-> Result<$1, String>
```

### 2. 具体文件修复清单

#### src/api/commands/auth_commands.rs
```rust
// 需要修复的函数签名：
pub async fn login(...) -> Result<LoginResponse, String>
pub async fn register(...) -> Result<RegisterResponse, String>
pub async fn logout(...) -> Result<(), String>
pub async fn get_current_user(...) -> Result<UserResponse, String>
pub async fn validate_token(...) -> Result<bool, String>
pub async fn refresh_token(...) -> Result<TokenResponse, String>
pub async fn change_password(...) -> Result<(), String>
```

#### src/api/commands/user_commands.rs
```rust
// 需要修复的函数签名：
pub async fn create_user(...) -> Result<UserResponse, String>
pub async fn get_user(...) -> Result<UserResponse, String>
pub async fn update_user(...) -> Result<UserResponse, String>
pub async fn delete_user(...) -> Result<(), String>
pub async fn list_users(...) -> Result<PagedResult<UserResponse>, String> // 已修复
pub async fn update_password(...) -> Result<(), String>
```

#### src/api/commands/project_commands.rs
```rust
// 需要修复的函数签名：
pub async fn create_project(...) -> Result<ProjectResponse, String>
pub async fn get_project(...) -> Result<ProjectResponse, String>
pub async fn update_project(...) -> Result<ProjectResponse, String>
pub async fn delete_project(...) -> Result<(), String>
pub async fn list_projects(...) -> Result<PagedResult<ProjectResponse>, String>
pub async fn update_project_status(...) -> Result<ProjectResponse, String>
pub async fn get_project_stats(...) -> Result<ProjectStatsResponse, String>
pub async fn search_projects(...) -> Result<Vec<ProjectResponse>, String>
```

#### src/api/commands/task_commands.rs
```rust
// 需要修复的函数签名：
pub async fn create_task(...) -> Result<TaskResponse, String>
pub async fn get_task(...) -> Result<TaskResponse, String>
pub async fn update_task(...) -> Result<TaskResponse, String>
pub async fn delete_task(...) -> Result<(), String>
pub async fn list_tasks(...) -> Result<PagedResult<TaskResponse>, String>
pub async fn update_task_status(...) -> Result<TaskResponse, String>
pub async fn get_task_stats(...) -> Result<TaskStatsResponse, String>
pub async fn search_tasks(...) -> Result<Vec<TaskResponse>, String>
```

#### src/api/commands/area_commands.rs
```rust
// 需要修复的函数签名：
pub async fn create_area(...) -> Result<AreaResponse, String>
pub async fn get_area(...) -> Result<AreaResponse, String>
pub async fn update_area(...) -> Result<AreaResponse, String>
pub async fn delete_area(...) -> Result<(), String>
pub async fn list_areas(...) -> Result<PagedResult<AreaResponse>, String>
pub async fn get_area_summary(...) -> Result<AreaSummaryResponse, String>
pub async fn search_areas(...) -> Result<Vec<AreaResponse>, String>
```

## 🔧 批量修复步骤

### 步骤1: 使用 PowerShell 批量替换

```powershell
# 进入 src-tauri 目录
cd src-tauri

# 批量替换所有 .rs 文件中的返回类型
Get-ChildItem -Path "src/api/commands" -Filter "*.rs" -Recurse | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $newContent = $content -replace 'Result<ApiResponse<([^>]+)>, \(\)>', 'Result<$1, String>'
    Set-Content -Path $_.FullName -Value $newContent
}
```

### 步骤2: 手动验证和调整

某些复杂的泛型类型可能需要手动调整：

```rust
// 复杂类型示例
Result<ApiResponse<PagedResult<UserResponse>>, ()>
// 应该改为
Result<PagedResult<UserResponse>, String>

Result<ApiResponse<Vec<ProjectResponse>>, ()>
// 应该改为  
Result<Vec<ProjectResponse>, String>
```

### 步骤3: 修复函数体中的返回语句

由于宏已经修复，函数体中的 `execute_command!` 和 `execute_authenticated_command!` 宏调用应该自动返回正确的类型。

## 📝 验证修复

### 1. 编译检查
```bash
cargo check
```

### 2. 预期结果
- 所有 Tauri 命令返回类型错误应该消失
- 可能还有一些未使用变量的警告，但不影响编译

### 3. 错误数量预期
- **修复前**: ~450+ 编译错误
- **修复后**: 应该大幅减少到 <50 个警告

## 🚨 注意事项

### 1. 备份文件
在批量修改前建议备份重要文件：
```bash
git add .
git commit -m "Before batch fixing Tauri command return types"
```

### 2. 检查特殊情况
某些函数可能有特殊的返回类型，需要手动检查：
- 返回 `()` 的函数
- 返回复杂嵌套类型的函数
- 有条件返回的函数

### 3. 测试验证
修复后需要测试：
- 编译是否成功
- Tauri 命令是否能正常注册
- 前端调用是否正常

## 🎉 预期效果

修复完成后：
- ✅ 所有 Tauri 命令符合框架要求
- ✅ 编译错误大幅减少
- ✅ 应用能够成功启动
- ✅ 前后端通信正常

## 📋 修复后检查清单

- [ ] 运行 `cargo check` 无编译错误
- [ ] 运行 `cargo build` 成功编译
- [ ] 运行 `pnpm tauri dev` 成功启动
- [ ] 前端界面正常显示
- [ ] API 命令能够正常调用

这个批量修复脚本应该能解决大部分剩余的编译错误，让应用能够成功编译和运行。
