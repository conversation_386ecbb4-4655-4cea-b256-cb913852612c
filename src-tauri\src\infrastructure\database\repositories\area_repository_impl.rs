// 领域仓储实现
// 实现领域数据访问的具体逻辑

use crate::domain::entities::area::{Area, CreateAreaData, UpdateAreaData, AreaQuery, AreaStats};
use crate::domain::repositories::area_repository::AreaRepository;
use crate::infrastructure::database::models::AreaModel;
use crate::infrastructure::database::mappers::{area_mapper as AreaMapper, Mapper};
use crate::infrastructure::database::DatabaseUtils;
use crate::shared::types::{EntityId, QueryParams};
use crate::shared::errors::{AppError, AppResult};
use async_trait::async_trait;
use sqlx::{SqlitePool, Row};
use chrono::Utc;

/// 领域仓储实现
pub struct AreaRepositoryImpl {
    pool: SqlitePool,
}

impl AreaRepositoryImpl {
    /// 创建新的领域仓储实现
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl AreaRepository for AreaRepositoryImpl {
    async fn create(&self, data: CreateAreaData) -> AppResult<Area> {
        // 检查名称是否已存在
        if self.name_exists(&data.name).await? {
            return Err(AppError::validation("领域名称已存在"));
        }

        // 创建领域实体
        let area = Area::new(data)?;
        let model = AreaMapper::to_model(&area);

        // 插入数据库
        sqlx::query!(
            r#"
            INSERT INTO areas (
                id, name, description, standard, icon_name, color_hex,
                sort_order, is_active, created_by, created_at, updated_at, archived_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            model.id,
            model.name,
            model.description,
            model.standard,
            model.icon_name,
            model.color_hex,
            model.sort_order,
            model.is_active,
            model.created_by,
            model.created_at,
            model.updated_at,
            model.archived_at
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to create area: {}", e)))?;

        Ok(area)
    }

    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<Area>> {
        let model = sqlx::query_as!(
            AreaModel,
            "SELECT * FROM areas WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find area by id: {}", e)))?;

        match model {
            Some(model) => Ok(Some(AreaMapper::to_domain(&model)?)),
            None => Ok(None),
        }
    }

    async fn find_by_name(&self, name: &str) -> AppResult<Option<Area>> {
        let model = sqlx::query_as!(
            AreaModel,
            "SELECT * FROM areas WHERE name = ?",
            name
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find area by name: {}", e)))?;

        match model {
            Some(model) => Ok(Some(AreaMapper::to_domain(&model)?)),
            None => Ok(None),
        }
    }

    async fn update(&self, id: &EntityId, data: UpdateAreaData) -> AppResult<Area> {
        // 先获取现有领域
        let mut area = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        // 检查名称是否已被其他领域使用
        if let Some(ref name) = data.name {
            if let Some(existing_area) = self.find_by_name(name).await? {
                if existing_area.id != *id {
                    return Err(AppError::validation("领域名称已被其他领域使用"));
                }
            }
        }

        // 更新领域实体
        area.update(data)?;
        let model = AreaMapper::to_model(&area);

        // 更新数据库
        sqlx::query!(
            r#"
            UPDATE areas SET
                name = ?, description = ?, standard = ?, icon_name = ?, color_hex = ?,
                sort_order = ?, is_active = ?, updated_at = ?
            WHERE id = ?
            "#,
            model.name,
            model.description,
            model.standard,
            model.icon_name,
            model.color_hex,
            model.sort_order,
            model.is_active,
            model.updated_at,
            model.id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update area: {}", e)))?;

        Ok(area)
    }

    async fn delete(&self, id: &EntityId) -> AppResult<()> {
        // 检查是否有关联的项目或任务
        let project_count = sqlx::query!(
            "SELECT COUNT(*) as count FROM projects WHERE area_id = ?",
            id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check project count: {}", e)))?;

        if project_count.count > 0 {
            return Err(AppError::business_logic("不能删除有关联项目的领域"));
        }

        let task_count = sqlx::query!(
            "SELECT COUNT(*) as count FROM tasks WHERE area_id = ?",
            id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check task count: {}", e)))?;

        if task_count.count > 0 {
            return Err(AppError::business_logic("不能删除有关联任务的领域"));
        }

        let result = sqlx::query!(
            "DELETE FROM areas WHERE id = ?",
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to delete area: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("领域"));
        }

        Ok(())
    }

    async fn find_all(&self, query: AreaQuery, params: QueryParams) -> AppResult<Vec<Area>> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件
        if let Some(name) = &query.name {
            conditions.push("name LIKE ?".to_string());
            query_params.push(format!("%{}%", name));
        }

        if let Some(is_active) = query.is_active {
            conditions.push("is_active = ?".to_string());
            query_params.push(is_active.to_string());
        }

        if let Some(created_by) = &query.created_by {
            conditions.push("created_by = ?".to_string());
            query_params.push(created_by.clone());
        }

        if let Some(created_after) = query.created_after {
            conditions.push("created_at >= ?".to_string());
            query_params.push(created_after.to_rfc3339());
        }

        if let Some(created_before) = query.created_before {
            conditions.push("created_at <= ?".to_string());
            query_params.push(created_before.to_rfc3339());
        }

        // 添加搜索条件
        if let Some(search) = &params.search {
            let search_condition = DatabaseUtils::build_search_condition(search, &["name", "description", "standard"]);
            if !search_condition.is_empty() {
                conditions.push(search_condition);
            }
        }

        // 构建完整查询
        let base_query = "SELECT * FROM areas";
        let sorts = params.sort.as_deref().unwrap_or(&[]);
        let sql = DatabaseUtils::build_query(base_query, conditions, sorts, &params.pagination);

        // 执行查询
        let mut query_builder = sqlx::query_as::<_, AreaModel>(&sql);
        for param in query_params {
            query_builder = query_builder.bind(param);
        }

        let models = query_builder
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to find areas: {}", e)))?;

        AreaMapper::to_domain_list(models)
    }

    async fn count(&self, query: AreaQuery) -> AppResult<u64> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件（与find_all相同的逻辑）
        if let Some(name) = &query.name {
            conditions.push("name LIKE ?".to_string());
            query_params.push(format!("%{}%", name));
        }

        if let Some(is_active) = query.is_active {
            conditions.push("is_active = ?".to_string());
            query_params.push(is_active.to_string());
        }

        if let Some(created_by) = &query.created_by {
            conditions.push("created_by = ?".to_string());
            query_params.push(created_by.clone());
        }

        if let Some(created_after) = query.created_after {
            conditions.push("created_at >= ?".to_string());
            query_params.push(created_after.to_rfc3339());
        }

        if let Some(created_before) = query.created_before {
            conditions.push("created_at <= ?".to_string());
            query_params.push(created_before.to_rfc3339());
        }

        let base_query = "SELECT COUNT(*) FROM areas";
        let sql = format!("{}{}", base_query, DatabaseUtils::build_where_clause(conditions));

        let mut query_builder = sqlx::query(&sql);
        for param in query_params {
            query_builder = query_builder.bind(param);
        }

        let row = query_builder
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count areas: {}", e)))?;

        let count: i64 = row.get(0);
        Ok(count as u64)
    }

    async fn find_by_creator(&self, creator_id: &EntityId, params: QueryParams) -> AppResult<Vec<Area>> {
        let query = AreaQuery {
            name: None,
            is_active: None,
            created_by: Some(creator_id.clone()),
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_active(&self, params: QueryParams) -> AppResult<Vec<Area>> {
        let query = AreaQuery {
            name: None,
            is_active: Some(true),
            created_by: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_archived(&self, params: QueryParams) -> AppResult<Vec<Area>> {
        let models = sqlx::query_as!(
            AreaModel,
            r#"
            SELECT * FROM areas 
            WHERE archived_at IS NOT NULL
            ORDER BY archived_at DESC
            LIMIT ? OFFSET ?
            "#,
            params.pagination.as_ref().map(|p| p.limit()).unwrap_or(20),
            params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find archived areas: {}", e)))?;

        AreaMapper::to_domain_list(models)
    }

    async fn activate(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE areas SET is_active = true, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to activate area: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("领域"));
        }

        Ok(())
    }

    async fn deactivate(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE areas SET is_active = false, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to deactivate area: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("领域"));
        }

        Ok(())
    }

    async fn archive(&self, id: &EntityId) -> AppResult<()> {
        let now = Utc::now();
        let result = sqlx::query!(
            "UPDATE areas SET is_active = false, archived_at = ?, updated_at = ? WHERE id = ?",
            now,
            now,
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to archive area: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("领域"));
        }

        Ok(())
    }

    async fn unarchive(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE areas SET is_active = true, archived_at = NULL, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to unarchive area: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("领域"));
        }

        Ok(())
    }

    async fn update_sort_order(&self, id: &EntityId, sort_order: i32) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE areas SET sort_order = ?, updated_at = ? WHERE id = ?",
            sort_order,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update area sort order: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("领域"));
        }

        Ok(())
    }

    async fn batch_update_sort_order(&self, updates: Vec<(EntityId, i32)>) -> AppResult<()> {
        if updates.is_empty() {
            return Ok(());
        }

        let mut tx = self.pool.begin().await
            .map_err(|e| AppError::database(format!("Failed to begin transaction: {}", e)))?;

        for (id, sort_order) in updates {
            sqlx::query!(
                "UPDATE areas SET sort_order = ?, updated_at = ? WHERE id = ?",
                sort_order,
                Utc::now(),
                id
            )
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::database(format!("Failed to update area sort order: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| AppError::database(format!("Failed to commit transaction: {}", e)))?;

        Ok(())
    }

    async fn name_exists(&self, name: &str) -> AppResult<bool> {
        let row = sqlx::query!(
            "SELECT COUNT(*) as count FROM areas WHERE name = ?",
            name
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check area name existence: {}", e)))?;

        Ok(row.count > 0)
    }

    async fn get_stats(&self, id: &EntityId) -> AppResult<AreaStats> {
        let area = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        Ok(area.get_stats())
    }

    async fn get_area_summary(&self, id: &EntityId) -> AppResult<AreaSummary> {
        let area = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        // 获取项目统计
        let project_stats = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
                COUNT(CASE WHEN status IN ('in_progress', 'at_risk', 'paused') THEN 1 END) as active_projects
            FROM projects 
            WHERE area_id = ?
            "#,
            id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get project stats: {}", e)))?;

        // 获取任务统计
        let task_stats = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_tasks,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
                COUNT(CASE WHEN status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as active_tasks
            FROM tasks 
            WHERE area_id = ?
            "#,
            id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get task stats: {}", e)))?;

        // 获取习惯统计
        let habit_stats = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_habits,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_habits
            FROM habits 
            WHERE area_id = ?
            "#,
            id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get habit stats: {}", e)))?;

        Ok(AreaSummary {
            area,
            total_projects: project_stats.total_projects as u64,
            completed_projects: project_stats.completed_projects as u64,
            active_projects: project_stats.active_projects as u64,
            total_tasks: task_stats.total_tasks as u64,
            completed_tasks: task_stats.completed_tasks as u64,
            active_tasks: task_stats.active_tasks as u64,
            total_habits: habit_stats.total_habits as u64,
            active_habits: habit_stats.active_habits as u64,
        })
    }

    async fn search(&self, keyword: &str, params: QueryParams) -> AppResult<Vec<Area>> {
        let search_params = QueryParams {
            search: Some(keyword.to_string()),
            ..params
        };

        let query = AreaQuery {
            name: None,
            is_active: None,
            created_by: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, search_params).await
    }
}

/// 领域摘要信息
#[derive(Debug, Clone)]
pub struct AreaSummary {
    pub area: Area,
    pub total_projects: u64,
    pub completed_projects: u64,
    pub active_projects: u64,
    pub total_tasks: u64,
    pub completed_tasks: u64,
    pub active_tasks: u64,
    pub total_habits: u64,
    pub active_habits: u64,
}
