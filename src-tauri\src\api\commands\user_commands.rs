// 用户相关的Tauri命令
// 实现用户管理的前后端通信

use crate::api::{ApiResponse, ApiState, PaginationRequest, CommandUtils};
use crate::domain::entities::user::{CreateUserData, UpdateUserData, UserQuery};
use crate::execute_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};

/// 创建用户请求
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: Option<String>,
    pub password: String,
    pub full_name: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
}

/// 更新用户请求
#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub email: Option<String>,
    pub full_name: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
}

/// 用户响应
#[derive(Debug, Serialize)]
pub struct UserResponse {
    pub id: String,
    pub username: String,
    pub email: Option<String>,
    pub full_name: Option<String>,
    pub timezone: String,
    pub language: String,
    pub is_active: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_login_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl From<crate::domain::entities::user::User> for UserResponse {
    fn from(user: crate::domain::entities::user::User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            email: user.email,
            full_name: user.full_name,
            timezone: user.timezone,
            language: user.language,
            is_active: user.is_active,
            created_at: user.created_at,
            updated_at: user.updated_at,
            last_login_at: user.last_login_at,
        }
    }
}

/// 创建用户
#[command]
pub async fn create_user(
    request: CreateUserRequest,
    state: State<'_, ApiState>,
) -> Result<UserResponse, String> {
    let context = CommandUtils::create_service_context(None);
    
    let create_data = CreateUserData {
        username: request.username,
        email: request.email,
        password: request.password,
        full_name: request.full_name,
        timezone: request.timezone,
        language: request.language,
    };

    let response = execute_command!(
        "create_user",
        None::<String>,
        state.user_service.create_user(&context, create_data)
    );

    Ok(response)
}

/// 获取用户信息
#[command]
pub async fn get_user(
    user_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Option<UserResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_command!(
        "get_user",
        current_user_id,
        state.user_service.get_user_by_id(&context, &user_id)
    );

    Ok(response)
}

/// 更新用户信息
#[command]
pub async fn update_user(
    user_id: String,
    request: UpdateUserRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<UserResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let update_data = UpdateUserData {
        email: request.email,
        full_name: request.full_name,
        timezone: request.timezone,
        language: request.language,
    };

    let response = execute_command!(
        "update_user",
        current_user_id,
        state.user_service.update_user(&context, &user_id, update_data)
    );

    Ok(response)
}

/// 删除用户
#[command]
pub async fn delete_user(
    user_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_command!(
        "delete_user",
        current_user_id,
        state.user_service.delete_user(&context, &user_id)
    );

    Ok(response)
}

/// 列出用户
#[command]
pub async fn list_users(
    pagination: PaginationRequest,
    username_filter: Option<String>,
    email_filter: Option<String>,
    active_only: Option<bool>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<UserResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let query = UserQuery {
        username: username_filter,
        email: email_filter,
        is_active: active_only,
        created_after: None,
        created_before: None,
    };

    let response = execute_command!(
        "list_users",
        current_user_id,
        state.user_service.list_users(&context, query, pagination.page(), pagination.page_size())
    );

    Ok(response)
}

/// 更新密码
#[command]
pub async fn update_password(
    user_id: String,
    new_password: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_command!(
        "update_password",
        current_user_id,
        state.user_service.update_password(&context, &user_id, &new_password)
    );

    Ok(response)
}

/// 激活用户
#[command]
pub async fn activate_user(
    user_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_command!(
        "activate_user",
        current_user_id,
        state.user_service.activate_user(&context, &user_id)
    );

    Ok(response)
}

/// 停用用户
#[command]
pub async fn deactivate_user(
    user_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_command!(
        "deactivate_user",
        current_user_id,
        state.user_service.deactivate_user(&context, &user_id)
    );

    Ok(response)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_response_conversion() {
        let user = crate::domain::entities::user::User {
            id: "user123".to_string(),
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hash".to_string(),
            full_name: Some("Test User".to_string()),
            avatar_url: None,
            timezone: "UTC".to_string(),
            language: "en".to_string(),
            is_active: true,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login_at: None,
        };

        let response = UserResponse::from(user.clone());
        assert_eq!(response.id, user.id);
        assert_eq!(response.username, user.username);
        assert_eq!(response.email, user.email);
        assert_eq!(response.full_name, user.full_name);
        assert_eq!(response.timezone, user.timezone);
        assert_eq!(response.language, user.language);
        assert_eq!(response.is_active, user.is_active);
    }

    #[test]
    fn test_create_user_request_validation() {
        let request = CreateUserRequest {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "SecurePass123!".to_string(),
            full_name: Some("Test User".to_string()),
            timezone: Some("UTC".to_string()),
            language: Some("en".to_string()),
        };

        assert_eq!(request.username, "testuser");
        assert_eq!(request.email, Some("<EMAIL>".to_string()));
        assert_eq!(request.password, "SecurePass123!");
    }

    #[test]
    fn test_update_user_request() {
        let request = UpdateUserRequest {
            email: Some("<EMAIL>".to_string()),
            full_name: Some("New Name".to_string()),
            timezone: Some("Asia/Shanghai".to_string()),
            language: Some("zh".to_string()),
        };

        assert_eq!(request.email, Some("<EMAIL>".to_string()));
        assert_eq!(request.full_name, Some("New Name".to_string()));
        assert_eq!(request.timezone, Some("Asia/Shanghai".to_string()));
        assert_eq!(request.language, Some("zh".to_string()));
    }
}
