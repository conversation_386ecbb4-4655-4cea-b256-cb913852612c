// 认证相关的Tauri命令
// 实现用户认证的前后端通信

use crate::api::{ApiResponse, ApiState, CommandUtils};
use crate::api::commands::user_commands::{CreateUserRequest, UserResponse};
use crate::domain::entities::user::CreateUserData;
use crate::execute_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};

/// 登录请求
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// 登录响应
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub user: UserResponse,
    pub token: String,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

/// 注册请求（复用CreateUserRequest）
pub type RegisterRequest = CreateUserRequest;

/// 用户登录
#[command]
pub async fn login(
    request: LoginRequest,
    state: State<'_, ApiState>,
) -> Result<LoginResponse, String> {
    let context = CommandUtils::create_service_context(None);

    let response = execute_command!(
        "login",
        None::<String>,
        async {
            // 验证用户密码
            let user_result = state.user_service.verify_password(&context, &request.username, &request.password).await?;
            
            match user_result.data {
                Some(user) => {
                    // 记录登录
                    let _ = state.user_service.record_login(&context, &user.id).await;
                    
                    // 生成简单的token（实际应用中应该使用JWT）
                    let token = format!("token_{}", user.id);
                    let expires_at = chrono::Utc::now() + chrono::Duration::hours(24);
                    
                    Ok(crate::application::services::ServiceResult::new(LoginResponse {
                        user: UserResponse::from(user),
                        token,
                        expires_at,
                    }))
                }
                None => {
                    Err(crate::shared::errors::AppError::unauthorized("用户名或密码错误"))
                }
            }
        }
    );

    Ok(response)
}

/// 用户注册
#[command]
pub async fn register(
    request: RegisterRequest,
    state: State<'_, ApiState>,
) -> Result<UserResponse, String> {
    let context = CommandUtils::create_service_context(None);
    
    let create_data = CreateUserData {
        username: request.username,
        email: request.email,
        password: request.password,
        full_name: request.full_name,
        timezone: request.timezone,
        language: request.language,
    };

    let response = execute_command!(
        "register",
        None::<String>,
        state.user_service.create_user(&context, create_data)
    );

    Ok(response)
}

/// 用户登出
#[command]
pub async fn logout(
    current_user_id: Option<String>,
) -> Result<(), String> {
    // 在实际应用中，这里应该清除token或session
    tracing::info!(
        user_id = ?current_user_id,
        "User logged out"
    );

    Ok(ApiResponse::success(()))
}

/// 获取当前用户信息
#[command]
pub async fn get_current_user(
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Option<UserResponse>, String> {
    if let Some(user_id) = current_user_id {
        let context = CommandUtils::create_service_context(Some(user_id.clone()));

        let response = execute_command!(
            "get_current_user",
            Some(user_id),
            state.user_service.get_user_by_id(&context, &user_id)
        );

        Ok(response)
    } else {
        Ok(ApiResponse::success(None))
    }
}

/// 刷新token
#[command]
pub async fn refresh_token(
    current_user_id: Option<String>,
) -> Result<String, String> {
    if let Some(user_id) = current_user_id {
        // 生成新的token（实际应用中应该使用JWT）
        let new_token = format!("token_{}_{}", user_id, chrono::Utc::now().timestamp());
        
        tracing::info!(
            user_id = %user_id,
            "Token refreshed"
        );

        Ok(ApiResponse::success(new_token))
    } else {
        Ok(ApiResponse::error("用户未认证".to_string()))
    }
}

/// 验证token
#[command]
pub async fn validate_token(
    token: String,
) -> Result<bool, String> {
    // 简单的token验证（实际应用中应该验证JWT）
    let is_valid = token.starts_with("token_") && token.len() > 10;
    
    tracing::debug!(
        token_valid = %is_valid,
        "Token validation"
    );

    Ok(ApiResponse::success(is_valid))
}

/// 修改密码
#[command]
pub async fn change_password(
    old_password: String,
    new_password: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    if let Some(user_id) = current_user_id {
        let context = CommandUtils::create_service_context(Some(user_id.clone()));

        let response = execute_command!(
            "change_password",
            Some(user_id.clone()),
            async {
                // 首先获取用户信息
                let user_result = state.user_service.get_user_by_id(&context, &user_id).await?;
                
                if let Some(user) = user_result.data {
                    // 验证旧密码
                    if user.verify_password(&old_password)? {
                        // 更新密码
                        state.user_service.update_password(&context, &user_id, &new_password).await
                    } else {
                        Err(crate::shared::errors::AppError::validation("旧密码不正确"))
                    }
                } else {
                    Err(crate::shared::errors::AppError::not_found("用户"))
                }
            }
        );

        Ok(response)
    } else {
        Ok(ApiResponse::error("用户未认证".to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_login_request() {
        let request = LoginRequest {
            username: "testuser".to_string(),
            password: "password123".to_string(),
        };

        assert_eq!(request.username, "testuser");
        assert_eq!(request.password, "password123");
    }

    #[test]
    fn test_login_response() {
        let user = crate::domain::entities::user::User {
            id: "user123".to_string(),
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hash".to_string(),
            full_name: Some("Test User".to_string()),
            avatar_url: None,
            timezone: "UTC".to_string(),
            language: "en".to_string(),
            is_active: true,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login_at: None,
        };

        let response = LoginResponse {
            user: UserResponse::from(user),
            token: "test_token".to_string(),
            expires_at: chrono::Utc::now() + chrono::Duration::hours(24),
        };

        assert_eq!(response.token, "test_token");
        assert_eq!(response.user.username, "testuser");
    }
}
