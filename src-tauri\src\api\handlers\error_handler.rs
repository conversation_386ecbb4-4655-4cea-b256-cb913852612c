// API错误处理器
// 统一处理API层的错误响应

use crate::shared::errors::AppError;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// API错误响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiErrorResponse {
    pub success: bool,
    pub error: ApiError,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub request_id: Option<String>,
}

/// API错误详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiError {
    pub code: String,
    pub message: String,
    pub details: Option<serde_json::Value>,
    pub field_errors: Option<HashMap<String, Vec<String>>>,
}

/// 错误处理器
pub struct ErrorHandler;

impl ErrorHandler {
    /// 将应用错误转换为API错误响应
    pub fn handle_error(error: AppError, request_id: Option<String>) -> ApiErrorResponse {
        let api_error = Self::convert_app_error(error);
        
        ApiErrorResponse {
            success: false,
            error: api_error,
            timestamp: chrono::Utc::now(),
            request_id,
        }
    }

    /// 转换应用错误为API错误
    fn convert_app_error(error: AppError) -> ApiError {
        match error {
            AppError::Validation { message, field_errors } => {
                ApiError {
                    code: "VALIDATION_ERROR".to_string(),
                    message,
                    details: None,
                    field_errors: Some(field_errors),
                }
            }
            AppError::NotFound { message } => {
                ApiError {
                    code: "NOT_FOUND".to_string(),
                    message,
                    details: None,
                    field_errors: None,
                }
            }
            AppError::Unauthorized { message } => {
                ApiError {
                    code: "UNAUTHORIZED".to_string(),
                    message,
                    details: None,
                    field_errors: None,
                }
            }
            AppError::Forbidden { message } => {
                ApiError {
                    code: "FORBIDDEN".to_string(),
                    message,
                    details: None,
                    field_errors: None,
                }
            }
            AppError::Conflict { message } => {
                ApiError {
                    code: "CONFLICT".to_string(),
                    message,
                    details: None,
                    field_errors: None,
                }
            }
            AppError::Database { message } => {
                ApiError {
                    code: "DATABASE_ERROR".to_string(),
                    message: "数据库操作失败".to_string(),
                    details: Some(serde_json::json!({ "original_message": message })),
                    field_errors: None,
                }
            }
            AppError::Internal { message } => {
                ApiError {
                    code: "INTERNAL_ERROR".to_string(),
                    message: "内部服务器错误".to_string(),
                    details: Some(serde_json::json!({ "original_message": message })),
                    field_errors: None,
                }
            }
            AppError::Configuration { message } => {
                ApiError {
                    code: "CONFIGURATION_ERROR".to_string(),
                    message,
                    details: None,
                    field_errors: None,
                }
            }
            AppError::External { message, source } => {
                ApiError {
                    code: "EXTERNAL_ERROR".to_string(),
                    message,
                    details: Some(serde_json::json!({ "source": source })),
                    field_errors: None,
                }
            }
        }
    }

    /// 创建验证错误响应
    pub fn validation_error(
        message: &str,
        field_errors: HashMap<String, Vec<String>>,
        request_id: Option<String>,
    ) -> ApiErrorResponse {
        let api_error = ApiError {
            code: "VALIDATION_ERROR".to_string(),
            message: message.to_string(),
            details: None,
            field_errors: Some(field_errors),
        };

        ApiErrorResponse {
            success: false,
            error: api_error,
            timestamp: chrono::Utc::now(),
            request_id,
        }
    }

    /// 创建未找到错误响应
    pub fn not_found_error(message: &str, request_id: Option<String>) -> ApiErrorResponse {
        let api_error = ApiError {
            code: "NOT_FOUND".to_string(),
            message: message.to_string(),
            details: None,
            field_errors: None,
        };

        ApiErrorResponse {
            success: false,
            error: api_error,
            timestamp: chrono::Utc::now(),
            request_id,
        }
    }

    /// 创建未授权错误响应
    pub fn unauthorized_error(message: &str, request_id: Option<String>) -> ApiErrorResponse {
        let api_error = ApiError {
            code: "UNAUTHORIZED".to_string(),
            message: message.to_string(),
            details: None,
            field_errors: None,
        };

        ApiErrorResponse {
            success: false,
            error: api_error,
            timestamp: chrono::Utc::now(),
            request_id,
        }
    }

    /// 创建禁止访问错误响应
    pub fn forbidden_error(message: &str, request_id: Option<String>) -> ApiErrorResponse {
        let api_error = ApiError {
            code: "FORBIDDEN".to_string(),
            message: message.to_string(),
            details: None,
            field_errors: None,
        };

        ApiErrorResponse {
            success: false,
            error: api_error,
            timestamp: chrono::Utc::now(),
            request_id,
        }
    }

    /// 创建内部错误响应
    pub fn internal_error(message: &str, request_id: Option<String>) -> ApiErrorResponse {
        let api_error = ApiError {
            code: "INTERNAL_ERROR".to_string(),
            message: "内部服务器错误".to_string(),
            details: Some(serde_json::json!({ "original_message": message })),
            field_errors: None,
        };

        ApiErrorResponse {
            success: false,
            error: api_error,
            timestamp: chrono::Utc::now(),
            request_id,
        }
    }

    /// 记录错误日志
    pub fn log_error(error: &AppError, request_id: Option<&str>, context: Option<&str>) {
        let context_str = context.unwrap_or("API");
        let request_id_str = request_id.unwrap_or("unknown");

        match error {
            AppError::Validation { .. } => {
                tracing::warn!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "Validation error occurred"
                );
            }
            AppError::NotFound { .. } => {
                tracing::info!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "Resource not found"
                );
            }
            AppError::Unauthorized { .. } | AppError::Forbidden { .. } => {
                tracing::warn!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "Authentication/authorization error"
                );
            }
            AppError::Database { .. } => {
                tracing::error!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "Database error occurred"
                );
            }
            AppError::Internal { .. } => {
                tracing::error!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "Internal error occurred"
                );
            }
            AppError::Configuration { .. } => {
                tracing::error!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "Configuration error occurred"
                );
            }
            AppError::External { .. } => {
                tracing::warn!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "External service error occurred"
                );
            }
            AppError::Conflict { .. } => {
                tracing::warn!(
                    request_id = %request_id_str,
                    context = %context_str,
                    error = %error,
                    "Conflict error occurred"
                );
            }
        }
    }

    /// 获取错误的HTTP状态码等价物（用于参考）
    pub fn get_status_code(error: &AppError) -> u16 {
        match error {
            AppError::Validation { .. } => 400,
            AppError::Unauthorized { .. } => 401,
            AppError::Forbidden { .. } => 403,
            AppError::NotFound { .. } => 404,
            AppError::Conflict { .. } => 409,
            AppError::Database { .. } => 500,
            AppError::Internal { .. } => 500,
            AppError::Configuration { .. } => 500,
            AppError::External { .. } => 502,
        }
    }

    /// 检查错误是否应该向用户显示详细信息
    pub fn should_show_details(error: &AppError) -> bool {
        match error {
            AppError::Validation { .. } 
            | AppError::NotFound { .. } 
            | AppError::Unauthorized { .. } 
            | AppError::Forbidden { .. } 
            | AppError::Conflict { .. } => true,
            AppError::Database { .. } 
            | AppError::Internal { .. } 
            | AppError::Configuration { .. } 
            | AppError::External { .. } => false,
        }
    }
}

/// 错误统计
#[derive(Debug, Clone, Serialize)]
pub struct ErrorStats {
    pub total_errors: u64,
    pub errors_by_type: HashMap<String, u64>,
    pub recent_errors: Vec<ErrorSummary>,
}

/// 错误摘要
#[derive(Debug, Clone, Serialize)]
pub struct ErrorSummary {
    pub error_code: String,
    pub message: String,
    pub count: u64,
    pub last_occurred: chrono::DateTime<chrono::Utc>,
}

/// 错误收集器
pub struct ErrorCollector {
    stats: std::sync::Arc<std::sync::RwLock<ErrorStats>>,
}

impl ErrorCollector {
    pub fn new() -> Self {
        Self {
            stats: std::sync::Arc::new(std::sync::RwLock::new(ErrorStats {
                total_errors: 0,
                errors_by_type: HashMap::new(),
                recent_errors: Vec::new(),
            })),
        }
    }

    /// 记录错误
    pub fn record_error(&self, error: &AppError) {
        let mut stats = self.stats.write().unwrap();
        stats.total_errors += 1;

        let error_code = match error {
            AppError::Validation { .. } => "VALIDATION_ERROR",
            AppError::NotFound { .. } => "NOT_FOUND",
            AppError::Unauthorized { .. } => "UNAUTHORIZED",
            AppError::Forbidden { .. } => "FORBIDDEN",
            AppError::Conflict { .. } => "CONFLICT",
            AppError::Database { .. } => "DATABASE_ERROR",
            AppError::Internal { .. } => "INTERNAL_ERROR",
            AppError::Configuration { .. } => "CONFIGURATION_ERROR",
            AppError::External { .. } => "EXTERNAL_ERROR",
        };

        *stats.errors_by_type.entry(error_code.to_string()).or_insert(0) += 1;

        // 更新最近错误列表
        let now = chrono::Utc::now();
        if let Some(existing) = stats.recent_errors.iter_mut()
            .find(|e| e.error_code == error_code) {
            existing.count += 1;
            existing.last_occurred = now;
        } else {
            stats.recent_errors.push(ErrorSummary {
                error_code: error_code.to_string(),
                message: error.to_string(),
                count: 1,
                last_occurred: now,
            });
        }

        // 保持最近错误列表大小
        if stats.recent_errors.len() > 100 {
            stats.recent_errors.sort_by(|a, b| b.last_occurred.cmp(&a.last_occurred));
            stats.recent_errors.truncate(100);
        }
    }

    /// 获取错误统计
    pub fn get_stats(&self) -> ErrorStats {
        self.stats.read().unwrap().clone()
    }

    /// 重置统计
    pub fn reset_stats(&self) {
        let mut stats = self.stats.write().unwrap();
        stats.total_errors = 0;
        stats.errors_by_type.clear();
        stats.recent_errors.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_conversion() {
        let app_error = AppError::validation("测试验证错误");
        let api_response = ErrorHandler::handle_error(app_error, Some("req123".to_string()));
        
        assert!(!api_response.success);
        assert_eq!(api_response.error.code, "VALIDATION_ERROR");
        assert_eq!(api_response.error.message, "测试验证错误");
        assert_eq!(api_response.request_id, Some("req123".to_string()));
    }

    #[test]
    fn test_status_code_mapping() {
        assert_eq!(ErrorHandler::get_status_code(&AppError::validation("test")), 400);
        assert_eq!(ErrorHandler::get_status_code(&AppError::unauthorized("test")), 401);
        assert_eq!(ErrorHandler::get_status_code(&AppError::forbidden("test")), 403);
        assert_eq!(ErrorHandler::get_status_code(&AppError::not_found("test")), 404);
        assert_eq!(ErrorHandler::get_status_code(&AppError::internal("test")), 500);
    }

    #[test]
    fn test_error_collector() {
        let collector = ErrorCollector::new();
        
        let error1 = AppError::validation("test1");
        let error2 = AppError::not_found("test2");
        
        collector.record_error(&error1);
        collector.record_error(&error2);
        collector.record_error(&error1); // 重复错误
        
        let stats = collector.get_stats();
        assert_eq!(stats.total_errors, 3);
        assert_eq!(stats.errors_by_type.get("VALIDATION_ERROR"), Some(&2));
        assert_eq!(stats.errors_by_type.get("NOT_FOUND"), Some(&1));
    }
}
