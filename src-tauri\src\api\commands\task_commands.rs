// 任务相关的Tauri命令
// 实现任务管理的前后端通信

use crate::api::{ApiResponse, ApiState, PaginationRequest, CommandUtils};
use crate::domain::entities::task::{CreateTaskData, UpdateTaskData, TaskQuery};
use crate::shared::types::{TaskStatus, Priority};
use crate::execute_authenticated_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};
use chrono::NaiveDate;

/// 创建任务请求
#[derive(Debug, Deserialize)]
pub struct CreateTaskRequest {
    pub title: String,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<String>,
    pub estimated_minutes: Option<u32>,
}

/// 更新任务请求
#[derive(Debug, Deserialize)]
pub struct UpdateTaskRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<String>,
    pub estimated_minutes: Option<u32>,
    pub completion_percentage: Option<u8>,
}

/// 任务响应
#[derive(Debug, Serialize)]
pub struct TaskResponse {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub status: String,
    pub priority: String,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<String>,
    pub estimated_minutes: Option<u32>,
    pub actual_minutes: u32,
    pub completion_percentage: u8,
    pub sort_order: i32,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl From<crate::domain::entities::task::Task> for TaskResponse {
    fn from(task: crate::domain::entities::task::Task) -> Self {
        Self {
            id: task.id,
            title: task.title,
            description: task.description,
            status: task.status.to_string(),
            priority: task.priority.to_string(),
            parent_task_id: task.parent_task_id,
            project_id: task.project_id,
            area_id: task.area_id,
            assigned_to: task.assigned_to,
            due_date: task.due_date.map(|d| d.to_string()),
            estimated_minutes: task.estimated_minutes,
            actual_minutes: task.actual_minutes,
            completion_percentage: task.completion_percentage,
            sort_order: task.sort_order,
            created_at: task.created_at,
            updated_at: task.updated_at,
            completed_at: task.completed_at,
        }
    }
}

/// 创建任务
#[command]
pub async fn create_task(
    request: CreateTaskRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<TaskResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let priority = request.priority.as_deref()
        .and_then(|p| match p {
            "low" => Some(Priority::Low),
            "medium" => Some(Priority::Medium),
            "high" => Some(Priority::High),
            "urgent" => Some(Priority::Urgent),
            _ => None,
        })
        .unwrap_or(Priority::Medium);

    let due_date = request.due_date
        .and_then(|d| NaiveDate::parse_from_str(&d, "%Y-%m-%d").ok());

    let create_data = CreateTaskData {
        title: request.title,
        description: request.description,
        priority: Some(priority),
        parent_task_id: request.parent_task_id,
        project_id: request.project_id,
        area_id: request.area_id,
        assigned_to: request.assigned_to,
        due_date,
        estimated_minutes: request.estimated_minutes,
    };

    let response = execute_authenticated_command!(
        "create_task",
        current_user_id,
        state.task_service.create_task(&context, create_data)
    );

    Ok(response)
}

/// 获取任务
#[command]
pub async fn get_task(
    task_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Option<TaskResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_task",
        current_user_id,
        state.task_service.get_task(&context, &task_id)
    );

    Ok(response)
}

/// 更新任务
#[command]
pub async fn update_task(
    task_id: String,
    request: UpdateTaskRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<TaskResponse, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let priority = request.priority.as_deref()
        .and_then(|p| match p {
            "low" => Some(Priority::Low),
            "medium" => Some(Priority::Medium),
            "high" => Some(Priority::High),
            "urgent" => Some(Priority::Urgent),
            _ => None,
        });

    let due_date = request.due_date
        .and_then(|d| NaiveDate::parse_from_str(&d, "%Y-%m-%d").ok());

    let update_data = UpdateTaskData {
        title: request.title,
        description: request.description,
        priority,
        parent_task_id: request.parent_task_id,
        project_id: request.project_id,
        area_id: request.area_id,
        assigned_to: request.assigned_to,
        due_date,
        estimated_minutes: request.estimated_minutes,
        completion_percentage: request.completion_percentage,
    };

    let response = execute_authenticated_command!(
        "update_task",
        current_user_id,
        state.task_service.update_task(&context, &task_id, update_data)
    );

    Ok(response)
}

/// 删除任务
#[command]
pub async fn delete_task(
    task_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "delete_task",
        current_user_id,
        state.task_service.delete_task(&context, &task_id)
    );

    Ok(response)
}

/// 列出任务
#[command]
pub async fn list_tasks(
    pagination: PaginationRequest,
    status_filter: Option<String>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<TaskResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let status = status_filter.as_deref()
        .and_then(|s| match s {
            "todo" => Some(TaskStatus::Todo),
            "in_progress" => Some(TaskStatus::InProgress),
            "waiting" => Some(TaskStatus::Waiting),
            "completed" => Some(TaskStatus::Completed),
            "cancelled" => Some(TaskStatus::Cancelled),
            _ => None,
        });

    let response = execute_authenticated_command!(
        "list_tasks",
        current_user_id,
        state.task_service.list_user_tasks(&context, pagination.page(), pagination.page_size(), status)
    );

    Ok(response)
}

/// 更新任务状态
#[command]
pub async fn update_task_status(
    task_id: String,
    status: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());
    
    let task_status = match status.as_str() {
        "todo" => TaskStatus::Todo,
        "in_progress" => TaskStatus::InProgress,
        "waiting" => TaskStatus::Waiting,
        "completed" => TaskStatus::Completed,
        "cancelled" => TaskStatus::Cancelled,
        _ => return Ok(ApiResponse::error("无效的任务状态".to_string())),
    };

    let response = execute_authenticated_command!(
        "update_task_status",
        current_user_id,
        state.task_service.update_task_status(&context, &task_id, task_status)
    );

    Ok(response)
}

/// 更新任务完成度
#[command]
pub async fn update_task_completion(
    task_id: String,
    percentage: u8,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "update_task_completion",
        current_user_id,
        state.task_service.update_task_completion(&context, &task_id, percentage)
    );

    Ok(response)
}

/// 获取逾期任务
#[command]
pub async fn get_overdue_tasks(
    pagination: PaginationRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<TaskResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_overdue_tasks",
        current_user_id,
        state.task_service.get_overdue_tasks(&context, pagination.page(), pagination.page_size())
    );

    Ok(response)
}

/// 获取任务层级结构
#[command]
pub async fn get_task_hierarchy(
    root_task_id: Option<String>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Vec<crate::domain::repositories::task_repository::TaskHierarchy>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_task_hierarchy",
        current_user_id,
        state.task_service.get_task_hierarchy(&context, root_task_id.as_deref())
    );

    Ok(response)
}

/// 获取任务统计
#[command]
pub async fn get_task_stats(
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::domain::repositories::task_repository::UserTaskStats, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "get_task_stats",
        current_user_id,
        state.task_service.get_user_task_stats(&context)
    );

    Ok(response)
}

/// 搜索任务
#[command]
pub async fn search_tasks(
    keyword: String,
    pagination: PaginationRequest,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<crate::application::services::PagedResult<TaskResponse>, String> {
    let context = CommandUtils::create_service_context(current_user_id.clone());

    let response = execute_authenticated_command!(
        "search_tasks",
        current_user_id,
        state.task_service.search_tasks(&context, &keyword, pagination.page(), pagination.page_size())
    );

    Ok(response)
}
