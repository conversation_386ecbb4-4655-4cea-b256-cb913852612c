import { defineConfig } from "vite";
import solid from "vite-plugin-solid";
import { resolve } from "path";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";

// @ts-expect-error process is a nodejs global
const host = process.env.TAURI_DEV_HOST;

// https://vite.dev/config/
export default defineConfig(async () => ({
  plugins: [solid()],

  // 路径别名配置
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
      "@/components": resolve(__dirname, "src/components"),
      "@/lib": resolve(__dirname, "src/lib"),
      "@/hooks": resolve(__dirname, "src/hooks"),
      "@/stores": resolve(__dirname, "src/stores"),
      "@/types": resolve(__dirname, "src/types"),
      "@/utils": resolve(__dirname, "src/utils"),
    },
  },

  // CSS配置
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer,
      ],
    },
  },

  // 构建配置
  build: {
    target: "esnext",
    minify: "esbuild",
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['solid-js'],
          ui: ['@kobalte/core'],
          utils: ['date-fns', 'clsx'],
        },
      },
    },
  },

  // 优化配置
  optimizeDeps: {
    include: [
      'solid-js',
      '@kobalte/core',
      'date-fns',
      'clsx',
    ],
    exclude: [
      '@tauri-apps/api',
      '@tauri-apps/plugin-opener',
    ],
  },

  // 环境变量配置
  envPrefix: ['VITE_', 'TAURI_'],

  // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
  //
  // 1. prevent Vite from obscuring rust errors
  clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 1420,
    strictPort: true,
    host: host || false,
    hmr: host
      ? {
          protocol: "ws",
          host,
          port: 1421,
        }
      : undefined,
    watch: {
      // 3. tell Vite to ignore watching `src-tauri`
      ignored: ["**/src-tauri/**"],
    },
  },
}));
