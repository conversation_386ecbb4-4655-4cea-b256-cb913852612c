// PaoLife 应用库入口
// 基于领域驱动设计(DDD)的模块化架构

pub mod api;
pub mod commands;
pub mod domain;
pub mod infrastructure;
pub mod application;
pub mod shared;

// 重新导出核心组件
pub use commands::*;
pub use shared::*;

// 应用程序初始化和运行
use infrastructure::config::{AppConfig, ConfigLoader};
use infrastructure::config::logging::LoggingManager;
use infrastructure::config::database::DatabaseManager;
use api::ApiState;

/// 应用程序状态
pub struct AppState {
    pub config: AppConfig,
    pub database: DatabaseManager,
    pub api_state: ApiState,
}

/// 初始化应用程序
async fn initialize_app() -> shared::errors::AppResult<AppState> {
    // 加载配置
    let config_path = ConfigLoader::get_default_config_path()?;
    let config = ConfigLoader::load_from_file(&config_path)?;

    // 初始化日志系统
    let logging_manager = LoggingManager::new(config.logging.clone());
    logging_manager.initialize()?;

    tracing::info!("Starting PaoLife application");
    tracing::debug!("Configuration loaded from: {:?}", config_path);

    // 初始化数据库
    let mut database_manager = DatabaseManager::new(config.database.clone());
    database_manager.initialize().await?;

    // 创建服务层
    use std::sync::Arc;
    use application::services::*;

    let user_service = Arc::new(UserService::new());
    let project_service = Arc::new(ProjectService::new());
    let task_service = Arc::new(TaskService::new());
    let area_service = Arc::new(AreaService::new());

    // 创建API状态
    let api_state = ApiState::new(
        user_service,
        project_service,
        task_service,
        area_service,
    );

    Ok(AppState {
        config,
        database: database_manager,
        api_state,
    })
}

// 临时保留的测试命令
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// 健康检查命令
#[tauri::command]
async fn health_check(state: tauri::State<'_, AppState>) -> Result<String, String> {
    match state.database.health_check().await {
        Ok(status) => {
            if status.is_healthy {
                Ok(format!("Healthy - Response time: {}ms", status.response_time_ms))
            } else {
                Err(status.error_message.unwrap_or_else(|| "Unknown error".to_string()))
            }
        }
        Err(e) => Err(e.to_string()),
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 使用tokio运行时来处理异步初始化
    let rt = tokio::runtime::Runtime::new().expect("Failed to create tokio runtime");

    let app_state = rt.block_on(async {
        initialize_app().await.expect("Failed to initialize application")
    });

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_window_state::Builder::default().build())
        .plugin(tauri_plugin_store::Builder::default().build())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .manage(app_state.api_state)
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            // 基础命令
            greet,
            health_check,
            // 认证命令
            api::commands::auth_commands::login,
            api::commands::auth_commands::register,
            api::commands::auth_commands::logout,
            api::commands::auth_commands::get_current_user,
            api::commands::auth_commands::validate_token,
            api::commands::auth_commands::refresh_token,
            api::commands::auth_commands::change_password,
            // 用户命令
            api::commands::user_commands::create_user,
            api::commands::user_commands::get_user,
            api::commands::user_commands::update_user,
            api::commands::user_commands::delete_user,
            api::commands::user_commands::list_users,
            api::commands::user_commands::update_password,
            // 项目命令
            api::commands::project_commands::create_project,
            api::commands::project_commands::get_project,
            api::commands::project_commands::update_project,
            api::commands::project_commands::delete_project,
            api::commands::project_commands::list_projects,
            api::commands::project_commands::update_project_status,
            api::commands::project_commands::get_project_stats,
            api::commands::project_commands::search_projects,
            // 任务命令
            api::commands::task_commands::create_task,
            api::commands::task_commands::get_task,
            api::commands::task_commands::update_task,
            api::commands::task_commands::delete_task,
            api::commands::task_commands::list_tasks,
            api::commands::task_commands::update_task_status,
            api::commands::task_commands::get_task_stats,
            api::commands::task_commands::search_tasks,
            // 领域命令
            api::commands::area_commands::create_area,
            api::commands::area_commands::get_area,
            api::commands::area_commands::update_area,
            api::commands::area_commands::delete_area,
            api::commands::area_commands::list_areas,
            api::commands::area_commands::get_area_summary,
            api::commands::area_commands::search_areas
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
